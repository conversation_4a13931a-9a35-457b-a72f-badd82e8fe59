#ifndef _IMPORT_DATA_H
#define _IMPORT_DATA_H

#include "define.h"

// #define MAX_ENTRIES 100

// 健康记录结构体
typedef struct
{
    char date[20];         // 日期（格式：YYYY-MM-DD）
    char name[21];         // 姓名（中文名通常不超过4个汉字，每个汉字3字节，15字节足够）
    int age;               // 年龄
    int gender;            // 性别 (0:女, 1:男)
    float height;          // 身高（cm）
    float weight;          // 体重（kg）
    float systolic_bp;     // 收缩压(mmHg)
    float diastolic_bp;    // 舒张压(mmHg)
    float fasting_glucose; // 空腹血糖(mmol/L)
    float hba1c;           // 糖化血红蛋白(A1c)(%)
    float cholesterol;     // 总胆固醇(mmol/L)
    float ldl;             // 低密度脂蛋白胆固醇(LDL)(mmol/L)
    float creatinine;      // 肌酐(umol/L)
    int heartRate;         // 心率（次/分钟）
    float sleepHours;      // 睡眠时长（小时）
    int is_smoker;         // 是否吸烟 (0:否, 1:是)
    int family_history;    // 家族史 (0:无, 1:有)
    float lp_a;            // 脂蛋白A(Lp(a))(mmol/L)
    float hs_crp;          // 高灵敏度C反应蛋白(HS-CRP)(mg/L)
} HealthRecord;

int getInputCore(const char *prompt, int min, int max, bool optional, bool *skipped);
float getFloatInputCore(const char *prompt, float min, float max, bool optional, bool *skipped);
void getNameInput(char *name, int max_length);

int getIntInput(const char *prompt, int min, int max);

int getOptionalIntInput(const char *prompt, int min, int max);

float getFloatInput(const char *prompt, float min, float max);

float getOptionalFloatInput(const char *prompt, float min, float max);

/**
 * @file Import_Data.c
 * @brief 健康数据录入模块实现
 */

/**
 * @brief 交互式健康数据录入函数
 *
 * 通过终端交互收集用户健康数据，包含15项健康指标。
 * 自动记录当前日期，并对所有输入进行有效性验证。
 *
 * @note 依赖外部变量 records[MAX_ENTRIES] 和 numRecords
 * @warning 输入缓冲区限制为100字符
 */
void inputHealthData(); // 输入健康数据

// /**
//  * @brief 安全获取浮点数输入
//  *
//  * 显示提示信息并获取用户输入的浮点数，确保输入在有效范围内。
//  *
//  * @param[in] prompt 输入提示信息
//  * @param[in] min 允许的最小值
//  * @param[in] max 允许的最大值
//  * @return float 验证通过的输入值
//  *
//  * @note 会持续提示直到输入有效
//  */
// float getFloatInput(const char *prompt, float min, float max); // 获取浮点数输入

// /**
//  * @brief 安全获取整数输入
//  *
//  * 显示提示信息并获取用户输入的整数，确保输入在有效范围内。
//  *
//  * @param[in] prompt 输入提示信息
//  * @param[in] min 允许的最小值
//  * @param[in] max 允许的最大值
//  * @return int 验证通过的输入值
//  *
//  * @note 会持续提示直到输入有效
//  */
// int getIntInput(const char *prompt, int min, int max); // 获取整数输入

#endif /* _IMPORT_DATA_H */