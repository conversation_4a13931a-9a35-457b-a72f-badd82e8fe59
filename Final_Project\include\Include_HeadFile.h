#ifndef __INCLUDE_HEADFILE_H
#define __INCLUDE_HEADFILE_H

#include "define.h"

typedef struct
{
    int age;               // 年龄
    int gender;            // 性别：0=女，1=男
    float height;          // 身高(m)
    float weight;          // 体重(kg)
    float systolic_bp;     // 收缩压(mmHg)
    float diastolic_bp;    // 舒张压(mmHg)
    float fasting_glucose; // 空腹血糖(mmol/L)
    float hba1c;           // 糖化血红蛋白(%)
    float cholesterol;     // 总胆固醇(mmol/L)
    float ldl;             // LDL-C(mmol/L)
    float creatinine;      // 血肌酐(μmol/L)
    int is_smoker;         // 是否吸烟：0=否，1=是
    int family_history;    // 家族史：0=无，1=有（心血管/糖尿病）
    float lp_a;            // 脂蛋白(a) (mg/dL)
    float hs_crp;          // 超敏C反应蛋白(mg/L)
} HealthData;

// 慢性病风险评估结果
typedef struct
{
    int cvd_risk_level; // 心脑血管风险等级：0=低危，1=中危，2=高危
    int diabetes_risk;  // 糖尿病风险：0=低，1=中，2=高
    int ckd_risk;       // 慢性肾病风险：0=无，1=早期，2=显著
    int oncology_alert; // 肿瘤高风险标志：0=否，1=是
    char advice[512];   // 个性化建议
} RiskResult;

#endif //__INCLUDE_HEADFILE_H__