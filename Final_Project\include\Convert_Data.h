#ifndef _CONVERT_DATA__H
#define _CONVERT_DATA__H

#include "define.h"

/**
 * @brief 将健康记录从原始格式转换为计算所需的格式
 *
 * 该函数负责将HealthRecord结构体中的原始数据转换为HealthData结构体中的计算用数据。
 * 主要转换包括单位转换（如身高从厘米转换为米，脂蛋白(a)从mmol/L转换为mg/dL）和直接复制。
 *
 * @param[in] record 指向源健康记录的指针，包含原始数据
 * @param[out] data 指向目标健康数据的指针，用于存储转换后的数据
 *
 * @note 如果输入指针为NULL，函数将直接返回而不执行任何操作
 *
 * @par 单位转换说明:
 * - 身高: cm → m (除以100)
 * - 脂蛋白(a): mmol/L → mg/dL (乘以38.67)
 */
void Convert_Data(const HealthRecord *record, HealthData *data); // 将HealthRecord转换为HealthData

/**
 * @brief 从健康记录数组中获取最新的记录
 *
 * 该函数从给定的健康记录数组中返回最新的记录（即数组中的最后一条记录）。
 *
 * @param[in] records 健康记录数组
 * @param[in] numRecords 数组中的记录数量
 *
 * @return 指向最新健康记录的指针，如果输入无效则返回NULL
 *
 * @note 调用方需要检查返回的指针是否为NULL
 * @warning 传入的记录数组必须按时间顺序排列，最新的记录在最后
 */
HealthRecord *getLatestRecord(HealthRecord records[], int numRecords); // 获取最新的健康记录

/**
 * @brief 打印操作后暂停提示
 *
 * @note 该函数会显示"按Enter继续"提示，并清空输入缓冲区，
 *       用于每个操作执行后的界面暂停，防止界面快速跳过
 */
void print_post_action_prompt();

/**
 * @brief 打印无数据警告信息
 *
 * @param operation 当前操作名称字符串(如"分析"、"查询")
 *
 * @note 当检测到无可用数据时调用，显示友好提示信息
 *       提示格式为："无[操作名称]数据！"
 */
void print_no_data_warning(const char *operation);

/**
 * @brief 打印操作标题头
 *
 * @param operation 操作名称字符串(如"健康信息录入")
 *
 * @note 用于在功能操作开始时打印带装饰边框的标题，
 *       格式为："\n=== [操作名称] ==="
 */
void print_operation_header(const char *operation);

#endif // !_CONVERT_DATA__H