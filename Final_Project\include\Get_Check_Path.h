#ifndef __GET_CHECK_PATH_H
#define __GET_CHECK_PATH_H

#include "define.h"

/**
 * @brief 动态检测并返回有效的 File_Save 目录路径。
 *
 * 优先尝试上一级目录 (`../File_Save`)，若不存在则尝试当前目录 (`File_Save`)。
 *
 * @return const char* 返回有效的路径字符串（无需手动释放内存），
 *         若目录不存在则返回 NULL。
 *
 * @note 示例返回值：
 *       - `"../File_Save"`（当程序运行在 build/ 目录时）
 *       - `"File_Save"`    （当程序运行在项目根目录时）
 *       - `NULL`           （目录不存在时）
 */
const char *getSaveDir();

#endif // !__GET_CHECK_PATH_H