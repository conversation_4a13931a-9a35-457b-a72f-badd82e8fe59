
#include "define.h"

// 初始化TrendAlert结构体
void initTrendAlert(TrendAlert *alert, const char *indicator,
                    float current, float prev, float change)
{
    strncpy(alert->indicator, indicator, sizeof(alert->indicator) - 1);
    alert->indicator[sizeof(alert->indicator) - 1] = '\0';
    alert->current_value = current;
    alert->prev_value = prev;
    alert->change_percent = change;
    alert->trend_status[0] = '\0';
    alert->recommendation[0] = '\0';
    alert->alert_level = 0;
}

// 计算两个日期之间的天数差
int daysBetween(const char *date1, const char *date2)
{
    struct tm tm1 = {0}, tm2 = {0};
    sscanf(date1, "%d-%d-%d", &tm1.tm_year, &tm1.tm_mon, &tm1.tm_mday);
    sscanf(date2, "%d-%d-%d", &tm2.tm_year, &tm2.tm_mon, &tm2.tm_mday);
    tm1.tm_year -= 1900;
    tm1.tm_mon -= 1;
    tm2.tm_year -= 1900;
    tm2.tm_mon -= 1;
    time_t t1 = mktime(&tm1);
    time_t t2 = mktime(&tm2);
    return (int)(difftime(t2, t1) / (60 * 60 * 24));
}

// 检查数据是否有效
bool is_valid_value(float value)
{
    return value > 0 && value != NO_INPUT_FLOAT;
}

// 获取有效的历史数据点
int get_valid_data_points(float *values, char *dates[], int count,
                          float *valid_values, char **valid_dates)
{
    int valid_count = 0;
    for (int i = 0; i < count; i++)
    {
        if (is_valid_value(values[i]))
        {
            valid_values[valid_count] = values[i];
            valid_dates[valid_count] = dates[i];
            valid_count++;
        }
    }
    return valid_count;
}

// 根据数据量和时间间隔选择合适的分析方法
void analyze_trend(const char *indicator, float *values, char *dates[], int count,
                   TrendAlert *alert)
{
    if (count < 2)
        return;

    // 过滤无效数据
    float valid_values[count];
    char *valid_dates[count];
    int valid_count = get_valid_data_points(values, dates, count, valid_values, valid_dates);

    if (valid_count < 2)
        return;

    // 计算时间间隔
    int total_days = daysBetween(valid_dates[0], valid_dates[valid_count - 1]);
    float avg_interval = (float)total_days / (valid_count - 1);

    // 最近一次变化
    float latest_change = (valid_values[valid_count - 1] - valid_values[valid_count - 2]) /
                          (valid_values[valid_count - 2] + 0.0001f) * 100; // 避免除以0
    initTrendAlert(alert, indicator, valid_values[valid_count - 1],
                   valid_values[valid_count - 2], latest_change);

    // 根据数据量和时间间隔选择分析策略
    if (valid_count == 2)
    {
        // 只有两条数据时简单比较
        if (fabs(latest_change) > 15)
        {
            strncpy(alert->trend_status, (latest_change > 0) ? "显著上升" : "显著下降",
                    sizeof(alert->trend_status) - 1);
            alert->alert_level = (fabs(latest_change) > 25) ? 2 : 1;
        }
        else
        {
            strncpy(alert->trend_status, "稳定", sizeof(alert->trend_status) - 1);
            alert->alert_level = 0;
        }
    }
    else if (valid_count >= 3 && valid_count <= 5)
    {
        // 3-5条数据时，检查是否符合医学上短期趋势(2-4周)
        if (avg_interval >= 3 && avg_interval <= 30)
        {
            float short_term_change = (valid_values[valid_count - 1] - valid_values[0]) /
                                      (valid_values[0] + 0.0001f) * 100;
            if (fabs(short_term_change) > 10)
            {
                strncpy(alert->trend_status, (short_term_change > 0) ? "短期上升" : "短期下降",
                        sizeof(alert->trend_status) - 1);
                alert->alert_level = (fabs(short_term_change) > 20) ? 2 : 1;
            }
            else
            {
                strncpy(alert->trend_status, "稳定", sizeof(alert->trend_status) - 1);
                alert->alert_level = 0;
            }
        }
    }
    else if (valid_count >= 6)
    {
        // 6条以上数据时，检查是否符合医学上中长期趋势(1-6个月)
        if (avg_interval >= 7 && avg_interval <= 90)
        {
            float long_term_change = (valid_values[valid_count - 1] - valid_values[0]) /
                                     (valid_values[0] + 0.0001f) * 100;
            if (fabs(long_term_change) > 15)
            {
                strncpy(alert->trend_status, (long_term_change > 0) ? "持续上升" : "持续下降",
                        sizeof(alert->trend_status) - 1);
                alert->alert_level = (fabs(long_term_change) > 30) ? 2 : 1;
            }
            else
            {
                strncpy(alert->trend_status, "稳定", sizeof(alert->trend_status) - 1);
                alert->alert_level = 0;
            }
        }
    }
}

void generate_trend_alert(const HealthRecord records[], int numRecords,
                          TrendAlert *alerts, int *alert_count)
{
    *alert_count = 0;

    if (numRecords < 2)
    {
        printf("需要至少2条数据才能进行趋势分析\n");
        return;
    }

    // 确定分析的数据范围（最多10条）
    int start_index = (numRecords > 10) ? numRecords - 10 : 0;
    int analysis_count = (numRecords > 10) ? 10 : numRecords;

    // 准备数据数组
    float systolic_bp[analysis_count];
    float diastolic_bp[analysis_count];
    float fasting_glucose[analysis_count];
    float ldl[analysis_count];
    float creatinine[analysis_count];
    float hs_crp[analysis_count];
    char *date_labels[analysis_count];

    // 提取数据
    for (int i = 0; i < analysis_count; i++)
    {
        int record_idx = start_index + i;
        date_labels[i] = records[record_idx].date;
        systolic_bp[i] = records[record_idx].systolic_bp;
        diastolic_bp[i] = records[record_idx].diastolic_bp;
        fasting_glucose[i] = records[record_idx].fasting_glucose;
        ldl[i] = records[record_idx].ldl;
        creatinine[i] = records[record_idx].creatinine;
        hs_crp[i] = records[record_idx].hs_crp;
    }

    // 分析各指标趋势（只要有至少两个有效数据点就分析）
    int valid_systolic = 0, valid_diastolic = 0, valid_glucose = 0;
    int valid_ldl = 0, valid_creatinine = 0, valid_hs_crp = 0;

    // 检查每个指标是否有足够有效数据
    for (int i = 0; i < analysis_count; i++)
    {
        if (is_valid_value(systolic_bp[i]))
            valid_systolic++;
        if (is_valid_value(diastolic_bp[i]))
            valid_diastolic++;
        if (is_valid_value(fasting_glucose[i]))
            valid_glucose++;
        if (is_valid_value(ldl[i]))
            valid_ldl++;
        if (is_valid_value(creatinine[i]))
            valid_creatinine++;
        if (is_valid_value(hs_crp[i]))
            valid_hs_crp++;
    }

    // 只分析有足够有效数据的指标
    if (valid_systolic >= 2)
    {
        analyze_trend("收缩压", systolic_bp, date_labels, analysis_count,
                      &alerts[(*alert_count)++]);
    }
    if (valid_diastolic >= 2)
    {
        analyze_trend("舒张压", diastolic_bp, date_labels, analysis_count,
                      &alerts[(*alert_count)++]);
    }
    if (valid_glucose >= 2)
    {
        analyze_trend("空腹血糖", fasting_glucose, date_labels, analysis_count,
                      &alerts[(*alert_count)++]);
    }
    if (valid_ldl >= 2)
    {
        analyze_trend("LDL胆固醇", ldl, date_labels, analysis_count,
                      &alerts[(*alert_count)++]);
    }
    if (valid_creatinine >= 2)
    {
        analyze_trend("肌酐", creatinine, date_labels, analysis_count,
                      &alerts[(*alert_count)++]);
    }
    if (valid_hs_crp >= 2)
    {
        analyze_trend("炎症指标", hs_crp, date_labels, analysis_count,
                      &alerts[(*alert_count)++]);
    }
}

void print_trend_report(const TrendAlert *alerts, int alert_count,
                        const HealthRecord records[], int numRecords)
{
    printf("\n================ 健康趋势分析报告 ================\n");

    // 计算分析的时间范围
    int analysis_count = (numRecords > 10) ? 10 : numRecords;
    int start_index = (numRecords > 10) ? numRecords - 10 : 0;
    int days = daysBetween(records[start_index].date, records[numRecords - 1].date);

    printf("分析周期: %s 至 %s (%d天, %d次测量)\n",
           records[start_index].date, records[numRecords - 1].date, days, analysis_count);

    for (int i = 0; i < alert_count; i++)
    {
        const char *color_code = "";
        const char *reset_code = COLOR_RESET;

        if (alerts[i].alert_level == 2)
        {
            color_code = COLOR_BOLD_RED;
        }
        else if (alerts[i].alert_level == 1)
        {
            color_code = COLOR_YELLOW;
        }
        else
        {
            color_code = COLOR_GREEN;
        }

        const char *alert_icon = (alerts[i].alert_level == 2) ? "!!" : (alerts[i].alert_level == 1) ? "!"
                                                                                                    : "";

        printf("\n%s[%s%s] %s趋势: %s%s\n", color_code, alert_icon, alerts[i].indicator,
               alerts[i].trend_status, alert_icon, reset_code);

        printf("  当前值: %.1f | 前次值: %.1f | 变化: %+.1f%%\n",
               alerts[i].current_value, alerts[i].prev_value, alerts[i].change_percent);

        if (strlen(alerts[i].recommendation) > 0)
        {
            printf("  %s建议: %s%s\n", COLOR_CYAN, alerts[i].recommendation, reset_code);
        }
    }

    printf("\n=================================================\n");
}

void print_health_assessment(const HealthRecord records[], int numRecords)
{
    TrendAlert alerts[6]; // 最多6个指标
    int alert_count = 0;

    generate_trend_alert(records, numRecords, alerts, &alert_count);
    print_trend_report(alerts, alert_count, records, numRecords);
}

// 心脑血管疾病风险趋势分析
void analyze_cvd_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count)
{
    *alert_count = 0;

    if (numRecords < 2)
        return;

    // 准备数据数组
    float systolic_bp[numRecords];
    float diastolic_bp[numRecords];
    float ldl[numRecords];
    float hs_crp[numRecords];
    char *date_labels[numRecords];

    // 提取有效数据点
    int valid_count = 0;
    for (int i = 0; i < numRecords; i++)
    {
        if (is_valid_value(records[i].systolic_bp) &&
            is_valid_value(records[i].diastolic_bp) &&
            is_valid_value(records[i].ldl) &&
            is_valid_value(records[i].hs_crp))
        {
            systolic_bp[valid_count] = records[i].systolic_bp;
            diastolic_bp[valid_count] = records[i].diastolic_bp;
            ldl[valid_count] = records[i].ldl;
            hs_crp[valid_count] = records[i].hs_crp;
            date_labels[valid_count] = records[i].date;
            valid_count++;
        }
    }

    if (valid_count < 2)
        return;

    // 分析关键指标趋势
    analyze_trend("收缩压(心脑血管)", systolic_bp, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("舒张压(心脑血管)", diastolic_bp, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("LDL胆固醇", ldl, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("炎症指标(hs-CRP)", hs_crp, date_labels, valid_count, &alerts[(*alert_count)++]);
}

// 糖尿病风险趋势分析
void analyze_diabetes_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count)
{
    *alert_count = 0;

    if (numRecords < 2)
        return;

    // 准备数据数组
    float fasting_glucose[numRecords];
    float hba1c[numRecords];
    float bmi[numRecords];
    char *date_labels[numRecords];

    // 提取有效数据点
    int valid_count = 0;
    for (int i = 0; i < numRecords; i++)
    {
        if (is_valid_value(records[i].fasting_glucose) &&
            is_valid_value(records[i].hba1c) &&
            is_valid_value(records[i].weight) &&
            is_valid_value(records[i].height))
        {
            fasting_glucose[valid_count] = records[i].fasting_glucose;
            hba1c[valid_count] = records[i].hba1c;
            bmi[valid_count] = records[i].weight / (records[i].height * records[i].height);
            date_labels[valid_count] = records[i].date;
            valid_count++;
        }
    }

    if (valid_count < 2)
        return;

    // 分析关键指标趋势
    analyze_trend("空腹血糖", fasting_glucose, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("糖化血红蛋白", hba1c, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("BMI指数(糖尿病)", bmi, date_labels, valid_count, &alerts[(*alert_count)++]);
}

// 慢性肾病风险趋势分析
void analyze_ckd_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count)
{
    *alert_count = 0;

    if (numRecords < 2)
        return;

    // 准备数据数组
    float creatinine[numRecords];
    float egfr[numRecords];
    float systolic_bp[numRecords]; // 血压对肾病影响大
    char *date_labels[numRecords];

    // 提取有效数据点
    int valid_count = 0;
    for (int i = 0; i < numRecords; i++)
    {
        if (is_valid_value(records[i].creatinine) &&
            is_valid_value(records[i].systolic_bp) &&
            is_valid_value(records[i].age))
        {
            creatinine[valid_count] = records[i].creatinine;
            egfr[valid_count] = calculate_egfr(records[i].age, records[i].gender, records[i].creatinine);
            systolic_bp[valid_count] = records[i].systolic_bp;
            date_labels[valid_count] = records[i].date;
            valid_count++;
        }
    }

    if (valid_count < 2)
        return;

    // 分析关键指标趋势
    analyze_trend("血清肌酐", creatinine, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("估算肾小球滤过率", egfr, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("收缩压(肾病)", systolic_bp, date_labels, valid_count, &alerts[(*alert_count)++]);
}

// 肿瘤风险趋势分析
void analyze_oncology_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count)
{
    *alert_count = 0;

    if (numRecords < 2)
        return;

    // 准备数据数组
    float bmi[numRecords];
    float hs_crp[numRecords]; // 炎症指标
    char *date_labels[numRecords];

    // 提取有效数据点
    int valid_count = 0;
    for (int i = 0; i < numRecords; i++)
    {
        if (is_valid_value(records[i].weight) &&
            is_valid_value(records[i].height) &&
            is_valid_value(records[i].hs_crp))
        {
            bmi[valid_count] = records[i].weight / (records[i].height * records[i].height);
            hs_crp[valid_count] = records[i].hs_crp;
            date_labels[valid_count] = records[i].date;
            valid_count++;
        }
    }

    if (valid_count < 2)
        return;

    // 分析关键指标趋势
    analyze_trend("BMI指数(肿瘤)", bmi, date_labels, valid_count, &alerts[(*alert_count)++]);
    analyze_trend("炎症指标(hs-CRP)", hs_crp, date_labels, valid_count, &alerts[(*alert_count)++]);
}

// 综合慢性病趋势分析
void analyze_chronic_disease_trends(const HealthRecord records[], int numRecords,
                                    TrendAlert *alerts, int *alert_count)
{
    *alert_count = 0;

    // 分析各类慢性病趋势
    analyze_cvd_trend(records, numRecords, alerts, alert_count);
    analyze_diabetes_trend(records, numRecords, alerts, alert_count);
    analyze_ckd_trend(records, numRecords, alerts, alert_count);
    analyze_oncology_trend(records, numRecords, alerts, alert_count);
}

// 打印慢性病趋势分析报告（智能过滤无法评估的项目）
void print_chronic_trend_report(const TrendAlert *alerts, int alert_count,
                                const HealthRecord records[], int numRecords)
{
    if (alert_count <= 0)
    {
        printf("\n%s未检测到足够的有效数据进行趋势分析%s\n", COLOR_YELLOW, COLOR_RESET);
        return;
    }

    // 检查各疾病类别是否有有效数据
    bool has_cvd = false, has_diabetes = false, has_ckd = false, has_oncology = false;

    for (int i = 0; i < alert_count; i++)
    {
        if (strstr(alerts[i].indicator, "心脑血管"))
            has_cvd = true;
        else if (strstr(alerts[i].indicator, "糖尿病"))
            has_diabetes = true;
        else if (strstr(alerts[i].indicator, "肾病"))
            has_ckd = true;
        else if (strstr(alerts[i].indicator, "肿瘤"))
            has_oncology = true;
    }

    printf("\n================ 慢性病趋势分析报告 ================\n");

    // 打印分析周期信息
    int analysis_count = (numRecords > 10) ? 10 : numRecords;
    int start_index = (numRecords > 10) ? numRecords - 10 : 0;
    int days = daysBetween(records[start_index].date, records[numRecords - 1].date);

    printf("分析周期: %s 至 %s (%d天, %d次测量)\n",
           records[start_index].date, records[numRecords - 1].date, days, analysis_count);

    // 打印各疾病趋势（只有有数据的才会打印）
    if (has_cvd)
    {
        printf("\n%s[心脑血管疾病风险趋势]%s\n", COLOR_BOLD, COLOR_RESET);
        for (int i = 0; i < alert_count; i++)
        {
            if (strstr(alerts[i].indicator, "心脑血管"))
            {
                print_single_alert(&alerts[i]);
            }
        }
    }

    if (has_diabetes)
    {
        printf("\n%s[糖尿病风险趋势]%s\n", COLOR_BOLD, COLOR_RESET);
        for (int i = 0; i < alert_count; i++)
        {
            if (strstr(alerts[i].indicator, "糖尿病"))
            {
                print_single_alert(&alerts[i]);
            }
        }
    }

    if (has_ckd)
    {
        printf("\n%s[慢性肾病风险趋势]%s\n", COLOR_BOLD, COLOR_RESET);
        for (int i = 0; i < alert_count; i++)
        {
            if (strstr(alerts[i].indicator, "肾病"))
            {
                print_single_alert(&alerts[i]);
            }
        }
    }

    if (has_oncology)
    {
        printf("\n%s[肿瘤风险趋势]%s\n", COLOR_BOLD, COLOR_RESET);
        for (int i = 0; i < alert_count; i++)
        {
            if (strstr(alerts[i].indicator, "肿瘤"))
            {
                print_single_alert(&alerts[i]);
            }
        }
    }

    // 打印未评估的疾病提示
    printf("\n%s[未评估项目说明]%s\n", COLOR_GRAY, COLOR_RESET);
    if (!has_cvd)
        printf("- 心脑血管: 数据不足\n");
    if (!has_diabetes)
        printf("- 糖尿病: 数据不足\n");
    if (!has_ckd)
        printf("- 慢性肾病: 数据不足\n");
    if (!has_oncology)
        printf("- 肿瘤: 数据不足\n");

    printf("\n=================================================\n");
}

// 辅助函数：打印单个趋势警报
void print_single_alert(const TrendAlert *alert)
{
    const char *color_code = "";
    const char *reset_code = COLOR_RESET;

    // 根据警报级别设置颜色
    if (alert->alert_level == 2)
    {
        color_code = COLOR_BOLD_RED;
    }
    else if (alert->alert_level == 1)
    {
        color_code = COLOR_YELLOW;
    }
    else
    {
        color_code = COLOR_GREEN;
    }

    const char *alert_icon = (alert->alert_level == 2) ? "!!" : (alert->alert_level == 1) ? "!"
                                                                                          : "";

    printf("  %s%-25s: %s%s\n", color_code, alert->indicator, alert->trend_status, reset_code);
    printf("    当前值: %.1f | 前次值: %.1f | 变化: %+.1f%%\n",
           alert->current_value, alert->prev_value, alert->change_percent);

    if (strlen(alert->recommendation) > 0)
    {
        printf("    %s建议: %s%s\n", COLOR_CYAN, alert->recommendation, reset_code);
    }
}