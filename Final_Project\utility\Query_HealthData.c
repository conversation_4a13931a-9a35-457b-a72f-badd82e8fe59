#include "define.h"

// 辅助函数：清除输入缓冲区
void clearInputBuffer()
{
    int c;
    while ((c = getchar()) != '\n' && c != EOF)
        ;
}

// 打印单条记录的完整信息
void printFullRecord(const HealthRecord *record)
{
    printf("\n=== 完整健康记录 ===\n");
    printf("日期: %s\n", record->date);
    printf("姓名: %s\n", record->name);
    printf("年龄: %d\n", record->age);
    printf("性别: %s\n", record->gender ? "男" : "女");

    printf("\n=== 身体指标 ===\n");
    printf("身高: %.1f cm\n", record->height);
    printf("体重: %.1f kg\n", record->weight);
    printf("心率: %d 次/分钟\n", record->heartRate);
    printf("睡眠时长: %.1f 小时\n", record->sleepHours);

    printf("\n=== 血压数据 ===\n");
    printf("收缩压: %.0f mmHg\n", record->systolic_bp);
    printf("舒张压: %.0f mmHg\n", record->diastolic_bp);

    printf("\n=== 血糖数据 ===\n");
    printf("空腹血糖: %.1f mmol/L\n", record->fasting_glucose);
    printf("糖化血红蛋白 A1c: %.1f%%\n", record->hba1c);

    printf("\n=== 血脂数据 ===\n");
    printf("总胆固醇: %.1f mmol/L\n", record->cholesterol);
    printf("低密度脂蛋白胆固醇: %.1f mmol/L\n", record->ldl);

    printf("\n=== 其他健康指标 ===\n");
    printf("肌酐: %.1f umol/L\n", record->creatinine);

    printf("\n=== 生活习惯 ===\n");
    printf("吸烟: %s\n", record->is_smoker ? "是" : "否");
    printf("心血管疾病家族史: %s\n", record->family_history ? "有" : "无");

    printf("\n=== 高级指标 ===\n");
    printf("脂蛋白A: %.1f mmol/L\n", record->lp_a);
    printf("高敏C反应蛋白: %.1f mg/L\n", record->hs_crp);

    printf("\n=== 结束 ===\n");
}

// 询问用户是否打印完整记录
void askToPrintFullRecord(const HealthRecord *record)
{
    printf("\n是否打印此记录的完整信息？(y/n): ");
    char choice;
    scanf(" %c", &choice);
    clearInputBuffer();

    if (choice == 'y' || choice == 'Y')
    {
        printFullRecord(record);
    }
}

// 按日期查询的函数
void queryByDate()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;

    char targetDate[20];
    printf("输入查询日期 (YYYY-MM-DD): ");
    if (scanf("%19s", targetDate) != 1)
    {
        printf("日期输入无效！\n");
        clearInputBuffer();
        return;
    }
    clearInputBuffer();

    printf("\n查询结果:\n");
    printf("序号\t日期\t\t姓名\t年龄\t性别\t身高\t体重\t收缩压\t舒张压\t空腹血糖\t心率\n");

    int found = 0;
    int foundIndices[MAX_ENTRIES]; // 存储找到的记录的索引
    int foundCount = 0;

    for (int i = 0; i < numRecords; i++)
    {
        if (strcmp(records[i].date, targetDate) == 0)
        {
            printf("%d\t%s\t%s\t%d\t%s\t%.1f\t%.1f\t%.0f\t%.0f\t%.1f\t\t%d\n",
                   foundCount + 1,
                   records[i].date,
                   records[i].name,
                   records[i].age,
                   records[i].gender ? "男" : "女",
                   records[i].height,
                   records[i].weight,
                   records[i].systolic_bp,
                   records[i].diastolic_bp,
                   records[i].fasting_glucose,
                   records[i].heartRate);
            foundIndices[foundCount++] = i;
            found = 1;
        }
    }

    if (!found)
    {
        printf("未找到指定日期的记录！\n");
        return;
    }

    // 询问用户是否查看/打印完整记录
    if (foundCount == 1)
    {
        askToPrintFullRecord(&records[foundIndices[0]]);
    }
    else
    {
        printf("\n请输入要查看完整记录的序号(1-%d)，或输入0返回: ", foundCount);
        int choice;
        if (scanf("%d", &choice) != 1)
        {
            printf("无效输入！\n");
            clearInputBuffer();
            return;
        }
        clearInputBuffer();

        if (choice > 0 && choice <= foundCount)
        {
            printFullRecord(&records[foundIndices[choice - 1]]);
        }
    }
}

// 按指标范围查询的函数
void queryByMetricRange()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;

    printf("\n=== 按指标范围查询 ===\n");
    printf("请选择要查询的指标:\n");
    printf("1. 收缩压 (正常值: 90-120 mmHg, 高血压: >140 mmHg)\n");
    printf("2. 舒张压 (正常值: 60-80 mmHg, 高血压: >90 mmHg)\n");
    printf("3. 空腹血糖 (正常值: 3.9-6.1 mmol/L, 糖尿病: ≥7.0 mmol/L)\n");
    printf("4. 心率 (正常值: 60-100 次/分, 心动过速: >100, 心动过缓: <60)\n");
    printf("5. 身高 (成人正常范围: 150-190 cm)\n");
    printf("6. 体重 (需结合身高计算BMI判断)\n");
    printf("7. 年龄\n");
    printf("请输入选择(1-7): ");

    int metricChoice;
    if (scanf("%d", &metricChoice) != 1 || metricChoice < 1 || metricChoice > 7)
    {
        printf("无效选择！\n");
        clearInputBuffer();
        return;
    }
    clearInputBuffer();

    float minVal, maxVal;
    printf("\n请输入查询范围:\n");
    printf("最小值: ");
    if (scanf("%f", &minVal) != 1)
    {
        printf("无效输入！\n");
        clearInputBuffer();
        return;
    }
    printf("最大值: ");
    if (scanf("%f", &maxVal) != 1)
    {
        printf("无效输入！\n");
        clearInputBuffer();
        return;
    }
    clearInputBuffer();

    if (minVal > maxVal)
    {
        printf("最小值不能大于最大值！\n");
        return;
    }

    printf("\n查询结果:\n");
    printf("序号\t日期\t\t姓名\t年龄\t性别\t指标值\t指标名称\n");

    int found = 0;
    int foundIndices[MAX_ENTRIES];
    int foundCount = 0;

    for (int i = 0; i < numRecords; i++)
    {
        float currentVal = 0;
        char *metricName = "";

        switch (metricChoice)
        {
        case 1:
            currentVal = records[i].systolic_bp;
            metricName = "收缩压(mmHg)";
            break;
        case 2:
            currentVal = records[i].diastolic_bp;
            metricName = "舒张压(mmHg)";
            break;
        case 3:
            currentVal = records[i].fasting_glucose;
            metricName = "空腹血糖(mmol/L)";
            break;
        case 4:
            currentVal = records[i].heartRate;
            metricName = "心率(次/分)";
            break;
        case 5:
            currentVal = records[i].height;
            metricName = "身高(cm)";
            break;
        case 6:
            currentVal = records[i].weight;
            metricName = "体重(kg)";
            break;
        case 7:
            currentVal = records[i].age;
            metricName = "年龄(岁)";
            break;
        }

        if (currentVal == NO_INPUT_FLOAT || currentVal == NO_INPUT_INT)
        {
            continue;
        }

        if (currentVal >= minVal && currentVal <= maxVal)
        {
            printf("%d\t%s\t%s\t%d\t%s\t%.1f\t%s\n",
                   foundCount + 1,
                   records[i].date,
                   records[i].name,
                   records[i].age,
                   records[i].gender ? "男" : "女",
                   currentVal,
                   metricName);
            foundIndices[foundCount++] = i;
            found = 1;
        }
    }

    if (!found)
    {
        printf("未找到符合范围的记录！\n");
        return;
    }

    // 询问用户是否查看/打印完整记录
    if (foundCount == 1)
    {
        askToPrintFullRecord(&records[foundIndices[0]]);
    }
    else
    {
        printf("\n请输入要查看完整记录的序号(1-%d)，或输入0返回: ", foundCount);
        int choice;
        if (scanf("%d", &choice) != 1)
        {
            printf("无效输入！\n");
            clearInputBuffer();
            return;
        }
        clearInputBuffer();

        if (choice > 0 && choice <= foundCount)
        {
            printFullRecord(&records[foundIndices[choice - 1]]);
        }
    }
}

void queryHealthData()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;

    if (numRecords == 0)
    {
        printf("当前没有健康记录可查询！\n");
        return;
    }

    while (1)
    {
        printf("\n=== 健康数据查询 ===\n");
        printf("1. 按日期查询\n");
        printf("2. 显示全部记录\n");
        printf("3. 按指标范围查询\n");
        printf("4. 按姓名查询\n");
        printf("5. 返回主菜单\n");
        printf("请选择查询方式: ");

        int choice;
        if (scanf("%d", &choice) != 1)
        {
            printf("无效输入！\n");
            clearInputBuffer();
            continue;
        }
        clearInputBuffer();

        switch (choice)
        {
        case 1:
            queryByDate();
            break;
        case 2:
            displayAllRecords();
            break;
        case 3:
            queryByMetricRange();
            break;
        case 4:
            queryByName();
            break;
        case 5:
            return;
        default:
            printf("无效选择，请输入1-5之间的数字！\n");
        }
    }
}

// 辅助函数：按姓名查询
void queryByName()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;

    char targetName[50];
    printf("输入要查询的姓名: ");
    if (scanf("%49s", targetName) != 1)
    {
        printf("姓名输入无效！\n");
        clearInputBuffer();
        return;
    }
    clearInputBuffer();

    printf("\n查询结果:\n");
    printf("序号\t日期\t\t姓名\t年龄\t性别\t身高\t体重\t收缩压\t舒张压\t空腹血糖\t心率\n");

    int found = 0;
    int foundIndices[MAX_ENTRIES];
    int foundCount = 0;

    for (int i = 0; i < numRecords; i++)
    {
        if (strstr(records[i].name, targetName) != NULL)
        {
            printf("%d\t%s\t%s\t%d\t%s\t%.1f\t%.1f\t%.0f\t%.0f\t%.1f\t\t%d\n",
                   foundCount + 1,
                   records[i].date,
                   records[i].name,
                   records[i].age,
                   records[i].gender ? "男" : "女",
                   records[i].height,
                   records[i].weight,
                   records[i].systolic_bp,
                   records[i].diastolic_bp,
                   records[i].fasting_glucose,
                   records[i].heartRate);
            foundIndices[foundCount++] = i;
            found = 1;
        }
    }

    if (!found)
    {
        printf("未找到包含 \"%s\" 的记录！\n", targetName);
        return;
    }

    // 询问用户是否查看/打印完整记录
    if (foundCount == 1)
    {
        askToPrintFullRecord(&records[foundIndices[0]]);
    }
    else
    {
        printf("\n请输入要查看完整记录的序号(1-%d)，或输入0返回: ", foundCount);
        int choice;
        if (scanf("%d", &choice) != 1)
        {
            printf("无效输入！\n");
            clearInputBuffer();
            return;
        }
        clearInputBuffer();

        if (choice > 0 && choice <= foundCount)
        {
            printFullRecord(&records[foundIndices[choice - 1]]);
        }
    }
}

// 辅助函数：显示所有记录
void displayAllRecords()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;

    printf("\n全部健康记录 (%d 条):\n", numRecords);
    printf("序号\t日期\t\t姓名\t年龄\t性别\t身高\t体重\t收缩压\t舒张压\t空腹血糖\t心率\n");

    for (int i = 0; i < numRecords; i++)
    {
        printf("%d\t%s\t%s\t%d\t%s\t%.1f\t%.1f\t%.0f\t%.0f\t%.1f\t\t%d\n",
               i + 1,
               records[i].date,
               records[i].name,
               records[i].age,
               records[i].gender ? "男" : "女",
               records[i].height,
               records[i].weight,
               records[i].systolic_bp,
               records[i].diastolic_bp,
               records[i].fasting_glucose,
               records[i].heartRate);
    }

    // 询问用户是否查看/打印完整记录
    printf("\n请输入要查看完整记录的序号(1-%d)，或输入0返回: ", numRecords);
    int choice;
    if (scanf("%d", &choice) != 1)
    {
        printf("无效输入！\n");
        clearInputBuffer();
        return;
    }
    clearInputBuffer();

    if (choice > 0 && choice <= numRecords)
    {
        printFullRecord(&records[choice - 1]);
    }
}