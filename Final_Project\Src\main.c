#include <windows.h>
#include "main.h"

HealthRecord records[MAX_ENTRIES] = {0};
int numRecords = 0;
int hasNewData = 0;

int main()
{
    SetConsoleOutputCP(65001);
    SetConsoleCP(65001);
    loadFromFile(NULL);

    while (1)
    {
        displayMenu();
        int choice = getChoice();

        switch (choice)
        {
        case 1: // 健康信息录入
            print_operation_header("健康信息录入");
            inputHealthData();
            break;

        case 2: // 健康数据分析(健康评估)
        {
            if (numRecords == 0)
            {
                print_no_data_warning("分析");
                break;
            }

            HealthRecord *latest = getLatestRecord(records, numRecords);
            if (latest == NULL)
                break;

            HealthData currentData;
            Convert_Data(latest, &currentData);

            // 执行健康评估并打印报告
            HealthAssessment assessment = evaluate_health(currentData);
            print_health_report(assessment);

            // 询问是否查看建议
            if (ask_user_continue("是否查看健康建议？"))
            {
                HealthAdvice advice = generate_targeted_advice(currentData);
                print_health_advice(&advice);
            }

            // 询问是否查看趋势分析
            if (numRecords >= 2 && ask_user_continue("是否查看健康指标趋势分析？"))
            {
                printf("\n正在分析健康指标趋势...\n");
                TrendAlert alerts[6];
                int alert_count = 0;
                generate_trend_alert(records, numRecords, alerts, &alert_count);
                print_trend_report(alerts, alert_count, records, numRecords);
            }
            break;
        }

        case 3: // 慢性病风险评估
        {
            if (numRecords == 0)
            {
                print_no_data_warning("风险评估");
                break;
            }

            HealthRecord *latest = getLatestRecord(records, numRecords);
            if (latest == NULL)
                break;

            HealthData currentData;
            Convert_Data(latest, &currentData);

            // 执行慢性病风险评估
            RiskResult risk = assess_chronic_disease_risk(currentData);
            print_risk_assessment(&risk);

            // 询问是否查看建议
            if (ask_user_continue("是否查看风险建议？"))
            {
                char risk_advice[1024];
                generate_risk_advice(&risk, risk_advice);
                printf("\n建议:\n%s", risk_advice);
            }

            // 询问是否查看趋势分析
            if (numRecords >= 2 && ask_user_continue("是否查看慢性病风险趋势分析？"))
            {
                printf("\n正在分析慢性病风险趋势...\n");
                TrendAlert alerts[10];
                int alert_count = 0;
                analyze_chronic_disease_trends(records, numRecords, alerts, &alert_count);
                print_chronic_trend_report(alerts, alert_count, records, numRecords);
            }
            break;
        }

        case 4: // 数据查询
            print_operation_header("数据查询");
            queryHealthData();
            break;

        case 5: // 显示加载的数据
            print_operation_header("已加载数据");
            printLoadedData();
            break;

        case 0: // 退出系统
            saveToFile(records, numRecords);
            printf("数据已保存，程序退出。\n");
            return 0;

        default:
            printf("无效选项，请重新输入！\n");
        }

        print_post_action_prompt();
    }
    return 0;
}