
#include "define.h"

// BMI评估（体重）
bool assess_bmi(HealthData data, float *score, char *comment)
{
    // 判断身高和体重是否为空
    if (data.height == NO_INPUT_FLOAT || data.weight == NO_INPUT_FLOAT)
    {
        return false;
    }

    // 获取BMI分类
    int bmi_cat = get_bmi_category(data.height, data.weight);
    *score = 100.0;

    // 根据BMI分类计算得分和评论
    switch (bmi_cat)
    {
    case 0: // 偏瘦
        *score -= 8;
        strcpy(comment, "体重偏瘦");
        break;
    case 2: // 超重
        *score -= 10;
        strcpy(comment, "体重超重");
        break;
    case 3: // 肥胖
        *score -= 15;
        strcpy(comment, "体重肥胖");
        break;
    default:
        strcpy(comment, "体重正常");
    }
    return true;
}

// 血压评估
bool assess_blood_pressure(HealthData data, float *score, char *comment)
{
    // 判断血压数据是否为空
    if (data.systolic_bp == NO_INPUT_FLOAT || data.diastolic_bp == NO_INPUT_FLOAT)
    {
        return false;
    }

    // 获取血压类别
    int bp_cat = get_bp_category(data.systolic_bp, data.diastolic_bp);
    *score = 100.0;

    // 根据血压类别进行评分
    switch (bp_cat)
    {
    case 1: // 正常高值
        *score -= 5;
        strcpy(comment, "血压正常高值");
        break;
    case 2: // 临界高血压
        *score -= 10;
        strcpy(comment, "血压临界高血压");
        break;
    case 3: // 高血压
        *score -= 20;
        strcpy(comment, "高血压");
        break;
    default:
        strcpy(comment, "血压正常");
    }
    return true;
}

// 血糖评估
bool assess_blood_sugar(HealthData data, float *score, char *comment)
{
    // 初始化变量
    bool has_data = false;
    *score = 100.0;
    comment[0] = '\0';

    // 空腹血糖评估
    if (data.fasting_glucose != NO_INPUT_FLOAT)
    {
        has_data = true;
        if (data.fasting_glucose >= 6.1 && data.fasting_glucose < 7.0)
        {
            // 空腹血糖受损
            *score -= 8;
            strcat(comment, "空腹血糖受损");
        }
        else if (data.fasting_glucose >= 7.0)
        {
            // 糖尿病风险
            *score -= 15;
            strcat(comment, "糖尿病风险");
        }
    }

    // 糖化血红蛋白评估
    if (data.hba1c != NO_INPUT_FLOAT)
    {
        has_data = true;
        if (comment[0] != '\0')
            strcat(comment, "，");

        if (data.hba1c >= 5.7 && data.hba1c < 6.5)
        {
            // 糖化血红蛋白偏高
            *score -= 5;
            strcat(comment, "糖化血红蛋白偏高");
        }
        else if (data.hba1c >= 6.5)
        {
            // 糖化血红蛋白提示糖尿病
            *score -= 10;
            strcat(comment, "糖化血红蛋白提示糖尿病");
        }
    }

    // 如果没有数据，返回false
    if (!has_data)
        return false;
    // 如果没有评论，设置评论为“血糖正常”
    if (comment[0] == '\0')
        strcpy(comment, "血糖正常");
    return true;
}

// 血脂评估
bool assess_blood_lipid(HealthData data, float *score, char *comment)
{
    // 初始化变量
    bool has_data = false;
    *score = 100.0;
    comment[0] = '\0';

    // LDL-C评估
    if (data.ldl != NO_INPUT_FLOAT)
    {
        // 如果LDL-C有输入
        has_data = true;
        if (data.ldl >= 3.4 && data.ldl < 4.1)
        {
            // 如果LDL-C在3.4-4.1之间
            *score -= 5;
            strcat(comment, "LDL-C边缘升高");
        }
        else if (data.ldl >= 4.1)
        {
            // 如果LDL-C大于等于4.1
            *score -= 10;
            strcat(comment, "LDL-C升高");
        }
    }

    // 总胆固醇评估
    if (data.cholesterol != NO_INPUT_FLOAT)
    {
        // 如果总胆固醇有输入
        has_data = true;
        if (comment[0] != '\0')
            strcat(comment, "，");

        if (data.cholesterol >= 5.2 && data.cholesterol < 6.2)
        {
            // 如果总胆固醇在5.2-6.2之间
            *score -= 5;
            strcat(comment, "总胆固醇偏高");
        }
        else if (data.cholesterol >= 6.2)
        {
            // 如果总胆固醇大于等于6.2
            *score -= 8;
            strcat(comment, "总胆固醇明显升高");
        }
    }

    // 如果没有输入数据，返回false
    if (!has_data)
        return false;
    // 如果没有评估结果，返回“血脂正常”
    if (comment[0] == '\0')
        strcpy(comment, "血脂正常");
    return true;
}

// 肾功能评估
// 评估肾功能
bool assess_kidney(HealthData data, float *score, char *comment)
{
    // 如果年龄、性别或肌酐值为空，则返回false
    if (data.creatinine == NO_INPUT_FLOAT || data.age == NO_INPUT_INT || data.gender == NO_INPUT_INT)
    {
        return false;
    }

    // 计算eGFR值
    float egfr = calculate_egfr(data.age, data.gender, data.creatinine);
    *score = 100.0;

    // 根据eGFR值判断肾功能情况
    if (egfr >= 60 && egfr < 90)
    {
        // 肾功能轻度下降
        *score -= 5;
        strcpy(comment, "肾功能轻度下降");
    }
    else if (egfr < 60)
    {
        // 肾功能中度下降
        *score -= 10;
        strcpy(comment, "肾功能中度下降");
    }
    else
    {
        // 肾功能正常
        strcpy(comment, "肾功能正常");
    }
    return true;
}

// 生活方式评估
bool assess_lifestyle(HealthData data, float *score, char *comment)
{
    // 初始化变量
    bool has_data = false;
    *score = 100.0;
    comment[0] = '\0';

    // 吸烟评估
    if (data.is_smoker != NO_INPUT_INT)
    {
        // 如果有数据，则将has_data置为true
        has_data = true;
        if (data.is_smoker)
        {
            // 如果是吸烟者，则将score减去8，并将"吸烟"添加到comment中
            *score -= 8;
            strcat(comment, "吸烟");
        }
    }

    // 家族史评估
    if (data.family_history != NO_INPUT_INT)
    {
        // 如果有数据，则将has_data置为true
        has_data = true;
        if (comment[0] != '\0')
            strcat(comment, "，");

        if (data.family_history)
        {
            // 如果有家族史，则将score减去5，并将"家族史"添加到comment中
            *score -= 5;
            strcat(comment, "家族史");
        }
    }

    // 如果没有数据，则返回false
    if (!has_data)
        return false;
    // 如果comment为空，则将"生活方式良好"添加到comment中
    if (comment[0] == '\0')
        strcpy(comment, "生活方式良好");
    // 返回true
    return true;
}

// 炎症评估
bool assess_inflammation(HealthData data, float *score, char *comment)
{
    // 初始化has_data为false，score为100.0
    bool has_data = false;
    *score = 100.0;

    // 脂蛋白(a)评估
    if (data.lp_a != NO_INPUT_FLOAT)
    {
        // 如果有数据，has_data为true
        has_data = true;
        // 如果脂蛋白(a)大于50，score减3，comment为"脂蛋白(a)偏高"
        if (data.lp_a > 50)
        {
            *score -= 3;
            strcpy(comment, "脂蛋白(a)偏高");
        }
    }

    // 超敏C反应蛋白评估
    if (data.hs_crp != NO_INPUT_FLOAT)
    {
        // 如果有数据，has_data为true
        if (has_data)
            strcat(comment, "，");
        has_data = true;

        // 如果超敏C反应蛋白大于等于2.0，score减4，comment为"炎症状态"
        if (data.hs_crp >= 2.0)
        {
            *score -= 4;
            strcat(comment, "炎症状态");
        }
    }

    // 如果没有数据，返回false
    if (!has_data)
        return false;
    // 如果comment为空，comment为"炎症指标正常"
    if (comment[0] == '\0')
        strcpy(comment, "炎症指标正常");
    // 返回true
    return true;
}

HealthAssessment evaluate_health(HealthData data)
{
    // 初始化结果结构体
    HealthAssessment result = {0};
    // 初始化有效维度计数器
    int valid_dimensions = 0;
    // 初始化总评分
    float total_score = 0.0f;

    // 1. 评估各独立维度
    // 评估BMI
    if (assess_bmi(data, &result.bmi.score, result.bmi.comment))
    {
        // 设置BMI数据有效
        result.bmi.has_data = true;
        // 将BMI评分加入总评分
        total_score += result.bmi.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 评估血压
    if (assess_blood_pressure(data, &result.blood_pressure.score, result.blood_pressure.comment))
    {
        // 设置血压数据有效
        result.blood_pressure.has_data = true;
        // 将血压评分加入总评分
        total_score += result.blood_pressure.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 评估血糖
    if (assess_blood_sugar(data, &result.blood_sugar.score, result.blood_sugar.comment))
    {
        // 设置血糖数据有效
        result.blood_sugar.has_data = true;
        // 将血糖评分加入总评分
        total_score += result.blood_sugar.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 评估血脂
    if (assess_blood_lipid(data, &result.blood_lipid.score, result.blood_lipid.comment))
    {
        // 设置血脂数据有效
        result.blood_lipid.has_data = true;
        // 将血脂评分加入总评分
        total_score += result.blood_lipid.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 评估肾脏
    if (assess_kidney(data, &result.kidney.score, result.kidney.comment))
    {
        // 设置肾脏数据有效
        result.kidney.has_data = true;
        // 将肾脏评分加入总评分
        total_score += result.kidney.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 评估生活方式
    if (assess_lifestyle(data, &result.lifestyle.score, result.lifestyle.comment))
    {
        // 设置生活方式数据有效
        result.lifestyle.has_data = true;
        // 将生活方式评分加入总评分
        total_score += result.lifestyle.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 评估炎症
    if (assess_inflammation(data, &result.inflammation.score, result.inflammation.comment))
    {
        // 设置炎症数据有效
        result.inflammation.has_data = true;
        // 将炎症评分加入总评分
        total_score += result.inflammation.score;
        // 有效维度计数器加一
        valid_dimensions++;
    }

    // 2. 计算综合评分（仅当有至少一个有效维度时）
    if (valid_dimensions > 0)
    {
        // 计算综合评分
        result.total_score = total_score / valid_dimensions;

        // 3. 确定综合评级
        // 综合评分大于等于90，评级为“非常健康”
        if (result.total_score >= 90)
        {
            strcpy(result.overall_rating, "非常健康");
        }
        // 综合评分大于等于80，评级为“健康”
        else if (result.total_score >= 80)
        {
            strcpy(result.overall_rating, "健康");
        }
        // 综合评分大于等于60，评级为“基本健康”
        else if (result.total_score >= 60)
        {
            strcpy(result.overall_rating, "基本健康");
        }
        // 综合评分小于60，评级为“不健康”
        else
        {
            strcpy(result.overall_rating, "不健康");
        }
    }
    else
    {
        // 无有效数据的情况
        result.total_score = 0;
        strcpy(result.overall_rating, "无法评估");
    }

    // 返回结果
    return result;
}

void print_health_report(HealthAssessment assessment)
{
    printf("\n\n" COLOR_BOLD "** 健康评估报告 **" COLOR_RESET "\n");
    printf("=========================================\n\n");

    // 打印各维度评估结果（仅显示有数据的维度）
    if (assessment.bmi.has_data)
    {
        printf("[体重评估] %d分 - %s\n", (int)assessment.bmi.score, assessment.bmi.comment);
    }

    if (assessment.blood_pressure.has_data)
    {
        printf("[血压评估] %d分 - %s\n", (int)assessment.blood_pressure.score, assessment.blood_pressure.comment);
    }

    if (assessment.blood_sugar.has_data)
    {
        printf("[血糖评估] %d分 - %s\n", (int)assessment.blood_sugar.score, assessment.blood_sugar.comment);
    }

    if (assessment.blood_lipid.has_data)
    {
        printf("[血脂评估] %d分 - %s\n", (int)assessment.blood_lipid.score, assessment.blood_lipid.comment);
    }

    if (assessment.kidney.has_data)
    {
        printf("[肾功能] %d分 - %s\n", (int)assessment.kidney.score, assessment.kidney.comment);
    }

    if (assessment.lifestyle.has_data)
    {
        printf("[生活方式] %d分 - %s\n", (int)assessment.lifestyle.score, assessment.lifestyle.comment);
    }

    if (assessment.inflammation.has_data)
    {
        printf("[炎症指标] %d分 - %s\n", (int)assessment.inflammation.score, assessment.inflammation.comment);
    }

    // 分隔线
    printf("\n------------------------------------------\n");

    // 打印综合评估结果
    printf(COLOR_BOLD "综合评级: " COLOR_RESET);
    if (assessment.total_score == 0)
    {
        printf(COLOR_WARNING "无法评估\n" COLOR_RESET);
    }
    else
    {
        // 根据评级显示不同颜色
        if (strcmp(assessment.overall_rating, "不健康") == 0)
        {
            printf(COLOR_ERROR "%s" COLOR_RESET, assessment.overall_rating);
        }
        else if (strcmp(assessment.overall_rating, "非常健康") == 0)
        {
            printf(COLOR_SUCCESS "%s" COLOR_RESET, assessment.overall_rating);
        }
        else
        {
            printf("%s", assessment.overall_rating);
        }

        printf("（平均分: %d分）\n", (int)assessment.total_score);
    }

    // 数据完整性提示
    printf("\n" COLOR_GRAY "注：" COLOR_RESET);
    if (assessment.total_score == 0)
    {
        printf(COLOR_WARNING "因数据不足无法进行全面评估\n" COLOR_RESET);
    }
    else
    {
        int missing_count = 7 - ((int)assessment.bmi.has_data +
                                 (int)assessment.blood_pressure.has_data +
                                 (int)assessment.blood_sugar.has_data +
                                 (int)assessment.blood_lipid.has_data +
                                 (int)assessment.kidney.has_data +
                                 (int)assessment.lifestyle.has_data +
                                 (int)assessment.inflammation.has_data);

        if (missing_count > 0)
        {
            printf(COLOR_WARNING "本次评估基于%d个维度（缺少%d个维度数据）\n" COLOR_RESET,
                   7 - missing_count, missing_count);
        }
        else
        {
            printf("已完成全面评估（7个维度）\n");
        }
    }

    // 免责声明和时间戳
    printf("\n" COLOR_GRAY "声明：本报告仅供参考，具体健康状况请咨询专业医生\n" COLOR_RESET);
    printf("=========================================\n");

    // 添加时间戳
    time_t now;
    time(&now);
    printf("\n" COLOR_GRAY "报告生成时间: %s" COLOR_RESET, ctime(&now));
}

void provide_targeted_advice(HealthData data)
{
    // 只对有数据的异常项目给出建议
    HealthAdvice advice = generate_targeted_advice(data);

    printf("\n" COLOR_BOLD "== 针对性健康建议 ==" COLOR_RESET "\n");

    if (strcmp(advice.summary, "所有检测指标均在正常范围内，请继续保持健康生活方式！") == 0)
    {
        printf(COLOR_SUCCESS "%s\n" COLOR_RESET, advice.summary);
    }
    else
    {
        printf("%s\n", advice.summary);
    }

    printf("=======================\n");
}

// 在 perform_health_evaluation 函数中调用
HealthAssessment perform_health_evaluation(HealthData data)
{
    HealthAssessment report = evaluate_health(data);

    char choice;
    printf("\n评估已完成，是否要打印健康报告？(y/n): ");
    scanf(" %c", &choice);

    if (choice == 'y' || choice == 'Y')
    {
        print_health_report(report);

        // 询问是否查看针对性建议
        printf("\n是否查看针对性健康建议？(y/n): ");
        scanf(" %c", &choice);
        if (choice == 'y' || choice == 'Y')
        {
            provide_targeted_advice(data);
        }
    }
    else
    {
        printf("\n已跳过打印健康报告。\n");
    }

    return report;
}