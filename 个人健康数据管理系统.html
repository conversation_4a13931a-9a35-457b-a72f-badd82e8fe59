<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人健康数据管理系统 - 项目展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 50%, #f0f8ff 100%);
            min-height: 100vh;
        }

        .tech-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card-bg {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .highlight-color {
            color: #00AEEF;
        }

        .title-black {
            color: #000000 !important;
        }

        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease-out;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .mermaid {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .icon-health {
            color: #e74c3c;
        }

        .icon-data {
            color: #3498db;
        }

        .icon-analysis {
            color: #2ecc71;
        }

        .icon-risk {
            color: #f39c12;
        }

        .icon-tech {
            color: #9b59b6;
        }

        .icon-innovation {
            color: #1abc9c;
        }

        .icon-system {
            color: #34495e;
        }

        .icon-medical {
            color: #e67e22;
        }

        .gradient-card-1 {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
            border-left: 4px solid #e74c3c;
        }

        .gradient-card-2 {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.05));
            border-left: 4px solid #3498db;
        }

        .gradient-card-3 {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(46, 204, 113, 0.05));
            border-left: 4px solid #2ecc71;
        }

        .gradient-card-4 {
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(243, 156, 18, 0.05));
            border-left: 4px solid #f39c12;
        }

        .gradient-card-5 {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.1), rgba(155, 89, 182, 0.05));
            border-left: 4px solid #9b59b6;
        }

        .gradient-card-6 {
            background: linear-gradient(135deg, rgba(26, 188, 156, 0.1), rgba(26, 188, 156, 0.05));
            border-left: 4px solid #1abc9c;
        }
    </style>
</head>

<body class="min-h-screen">
    <!-- 主标题区域 -->
    <header class="text-center py-16 tech-gradient">
        <h1 class="text-5xl font-bold text-white mb-4">个人健康数据管理系统</h1>
        <h2 class="text-2xl text-white opacity-90">Personal Health Data Management System</h2>
        <p class="text-lg text-white opacity-80 mt-4">基于C语言的智能健康监测与风险评估平台</p>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-6 py-12">

        <!-- 设计任务 -->
        <section class="mb-16 fade-in">
            <div class="card-bg rounded-xl p-8 border border-gray-200 shadow-lg">
                <h2 class="text-4xl font-bold title-black mb-8 flex items-center">
                    <i class="fas fa-bullseye mr-4 icon-health"></i>设计任务
                </h2>

                <!-- 研究背景 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">研究背景</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="gradient-card-1 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <div class="text-4xl font-bold icon-health mb-2">73%</div>
                            <div class="text-sm text-gray-500 mb-2">Chronic Disease Rate</div>
                            <p class="text-gray-700">慢性病患病率持续上升，个人健康管理需求迫切</p>
                        </div>
                        <div class="gradient-card-2 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <div class="text-4xl font-bold icon-data mb-2">AI驱动</div>
                            <div class="text-sm text-gray-500 mb-2">Intelligent Analysis</div>
                            <p class="text-gray-700">智能化健康评估与风险预测成为发展趋势</p>
                        </div>
                        <div class="gradient-card-3 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <div class="text-4xl font-bold icon-analysis mb-2">数据化</div>
                            <div class="text-sm text-gray-500 mb-2">Data-Driven Health</div>
                            <p class="text-gray-700">基于数据的精准健康管理模式兴起</p>
                        </div>
                    </div>
                </div>

                <!-- 功能框架 -->
                <div>
                    <h3 class="text-2xl font-semibold mb-4 title-black">功能框架</h3>
                    <div class="mermaid">
                        graph TD
                        A[个人健康数据管理系统] --> B[数据录入模块]
                        A --> C[健康评估模块]
                        A --> D[风险评估模块]
                        A --> E[数据查询模块]
                        A --> F[文件管理模块]

                        B --> B1[基本信息录入]
                        B --> B2[生理指标录入]
                        B --> B3[生活习惯录入]

                        C --> C1[BMI评估]
                        C --> C2[血压分析]
                        C --> C3[血糖评估]
                        C --> C4[综合健康评分]

                        D --> D1[心血管风险]
                        D --> D2[糖尿病风险]
                        D --> D3[慢性肾病风险]
                        D --> D4[个性化建议]

                        E --> E1[历史数据查询]
                        E --> E2[趋势分析]
                        E --> E3[数据统计]

                        F --> F1[CSV文件导入导出]
                        F --> F2[数据持久化存储]
                    </div>
                </div>
            </div>
        </section>

        <!-- 方案确定 -->
        <section class="mb-16 fade-in">
            <div class="card-bg rounded-xl p-8 border border-gray-200 shadow-lg">
                <h2 class="text-4xl font-bold title-black mb-8 flex items-center">
                    <i class="fas fa-cogs mr-4 icon-tech"></i>方案确定
                </h2>

                <!-- 算法设计 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">算法设计</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="gradient-card-4 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <h4 class="text-xl font-semibold mb-3 title-black">China-PAR模型</h4>
                            <div class="text-sm text-gray-500 mb-2">CVD Risk Assessment</div>
                            <p class="text-gray-700">基于中国人群的心血管疾病风险预测模型，考虑年龄、性别、血压、血脂等多维度因素</p>
                        </div>
                        <div class="gradient-card-5 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <h4 class="text-xl font-semibold mb-3 title-black">eGFR计算</h4>
                            <div class="text-sm text-gray-500 mb-2">Kidney Function</div>
                            <p class="text-gray-700">估算肾小球滤过率，评估慢性肾病风险，采用CKD-EPI公式进行精确计算</p>
                        </div>
                    </div>
                </div>

                <!-- 主要算法介绍 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">主要算法介绍</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-8">
                        <div class="gradient-card-1 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-heartbeat text-4xl icon-health mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">China-PAR</h4>
                            <div class="text-sm text-gray-500 mb-2">CVD Risk Model</div>
                            <p class="text-sm text-gray-600">中国人群心血管病风险预测模型，综合评估10年心血管事件风险</p>
                        </div>
                        <div class="gradient-card-2 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-calculator text-4xl icon-data mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">eGFR算法</h4>
                            <div class="text-sm text-gray-500 mb-2">Kidney Function</div>
                            <p class="text-sm text-gray-600">基于CKD-EPI公式的肾小球滤过率估算，精确评估肾功能状态</p>
                        </div>
                        <div class="gradient-card-3 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-chart-line text-4xl icon-analysis mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">BMI分级</h4>
                            <div class="text-sm text-gray-500 mb-2">Body Mass Index</div>
                            <p class="text-sm text-gray-600">基于亚洲人群标准的体重指数分级评估算法</p>
                        </div>
                        <div class="gradient-card-4 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-tint text-4xl icon-risk mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">血压分级</h4>
                            <div class="text-sm text-gray-500 mb-2">BP Classification</div>
                            <p class="text-sm text-gray-600">按照中国高血压防治指南的血压分级标准进行评估</p>
                        </div>
                        <div class="gradient-card-5 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-cube text-4xl icon-tech mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">血糖评估</h4>
                            <div class="text-sm text-gray-500 mb-2">Glucose Analysis</div>
                            <p class="text-sm text-gray-600">结合空腹血糖和糖化血红蛋白的糖尿病风险评估</p>
                        </div>
                        <div class="gradient-card-6 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-chart-area text-4xl icon-innovation mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">趋势分析</h4>
                            <div class="text-sm text-gray-500 mb-2">Trend Detection</div>
                            <p class="text-sm text-gray-600">基于历史数据的健康指标变化趋势智能分析算法</p>
                        </div>
                        <div class="gradient-card-1 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-exclamation-triangle text-4xl icon-health mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">风险预警</h4>
                            <div class="text-sm text-gray-500 mb-2">Risk Alert</div>
                            <p class="text-sm text-gray-600">多维度健康风险综合评估与智能预警系统</p>
                        </div>
                        <div class="gradient-card-2 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-user-md text-4xl icon-data mb-3"></i>
                            <h4 class="text-xl font-bold title-black mb-2">个性化建议</h4>
                            <div class="text-sm text-gray-500 mb-2">Personalized Advice</div>
                            <p class="text-sm text-gray-600">基于个人健康数据的精准化健康管理建议生成</p>
                        </div>
                    </div>
                </div>

                <!-- 核心算法流程 -->
                <div>
                    <h3 class="text-2xl font-semibold mb-4 title-black">核心算法流程</h3>
                    <div class="mermaid">
                        flowchart LR
                        A[用户数据输入] --> B[数据验证与预处理]
                        B --> C[健康指标计算]
                        C --> D[风险评估算法]
                        D --> E[个性化建议生成]
                        E --> F[结果输出与存储]

                        C --> C1[BMI计算]
                        C --> C2[血压分级]
                        C --> C3[血糖评估]
                        C --> C4[肾功能评估]

                        D --> D1[China-PAR评分]
                        D --> D2[糖尿病风险评估]
                        D --> D3[CKD风险分级]
                        D --> D4[综合风险等级]
                    </div>
                </div>
            </div>
        </section>

        <!-- 作品特色 -->
        <section class="mb-16 fade-in">
            <div class="card-bg rounded-xl p-8 border border-gray-200 shadow-lg">
                <h2 class="text-4xl font-bold title-black mb-8 flex items-center">
                    <i class="fas fa-star mr-4 icon-innovation"></i>作品特色
                </h2>

                <!-- 作品创新点 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">创新亮点</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="gradient-card-1 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-brain text-4xl icon-health mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">智能评估</h4>
                            <p class="text-sm text-gray-600">多维度健康风险智能评估算法</p>
                        </div>
                        <div class="gradient-card-2 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-chart-line text-4xl icon-data mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">趋势分析</h4>
                            <p class="text-sm text-gray-600">健康指标变化趋势动态监测</p>
                        </div>
                        <div class="gradient-card-3 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-user-md text-4xl icon-analysis mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">个性化建议</h4>
                            <p class="text-sm text-gray-600">基于个人数据的精准健康建议</p>
                        </div>
                        <div class="gradient-card-4 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-database text-4xl icon-risk mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">数据管理</h4>
                            <p class="text-sm text-gray-600">完整的数据存储与导入导出功能</p>
                        </div>
                    </div>
                </div>

                <!-- 核心模块 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">核心模块架构</h3>
                    <div class="mermaid">
                        graph TB
                        subgraph "用户界面层"
                        A[菜单系统] --> B[数据输入界面]
                        A --> C[结果展示界面]
                        end

                        subgraph "业务逻辑层"
                        D[健康评估引擎] --> E[风险评估算法]
                        D --> F[建议生成系统]
                        D --> G[趋势分析模块]
                        end

                        subgraph "数据访问层"
                        H[内存数据管理] --> I[文件I/O操作]
                        H --> J[CSV数据处理]
                        end

                        B --> D
                        C --> D
                        E --> H
                        F --> H
                        G --> H
                    </div>
                </div>

                <!-- 关键算法实现 -->
                <div>
                    <h3 class="text-2xl font-semibold mb-4 title-black">关键算法实现</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="gradient-card-5 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <h4 class="text-lg font-semibold mb-3 title-black flex items-center">
                                <i class="fas fa-heartbeat mr-2 icon-health"></i>心血管风险评估流程
                            </h4>
                            <div class="mermaid">
                                flowchart TD
                                A[输入生理指标] --> B[年龄性别评分]
                                B --> C[血压分级评分]
                                C --> D[血脂异常评分]
                                D --> E[血糖代谢评分]
                                E --> F[生活习惯评分]
                                F --> G[综合风险等级]
                                G --> H[个性化建议]
                            </div>
                        </div>
                        <div class="gradient-card-6 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <h4 class="text-lg font-semibold mb-3 title-black flex items-center">
                                <i class="fas fa-chart-area mr-2 icon-innovation"></i>健康趋势分析算法
                            </h4>
                            <div class="mermaid">
                                flowchart TD
                                A[历史数据获取] --> B[异常值检测]
                                B --> C[指标变化计算]
                                C --> D[趋势方向判断]
                                D --> E[预警信号生成]
                                E --> F[趋势报告输出]
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 心得体会 -->
        <section class="mb-16 fade-in">
            <div class="card-bg rounded-xl p-8 border border-gray-200 shadow-lg">
                <h2 class="text-4xl font-bold title-black mb-8 flex items-center">
                    <i class="fas fa-lightbulb mr-4 icon-system"></i>心得体会
                </h2>

                <!-- 项目总结 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">项目总结</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="gradient-card-1 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <div class="text-3xl font-bold icon-health mb-2">15+</div>
                            <div class="text-sm text-gray-500 mb-2">Core Modules</div>
                            <p class="text-gray-700">实现了15个核心功能模块，涵盖数据管理、健康评估、风险预测等完整功能链</p>
                        </div>
                        <div class="gradient-card-2 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <div class="text-3xl font-bold icon-data mb-2">3000+</div>
                            <div class="text-sm text-gray-500 mb-2">Lines of Code</div>
                            <p class="text-gray-700">编写超过3000行C语言代码，实现了完整的健康数据管理系统</p>
                        </div>
                        <div class="gradient-card-3 rounded-lg p-6 border border-gray-200 shadow-sm">
                            <div class="text-3xl font-bold icon-analysis mb-2">多算法</div>
                            <div class="text-sm text-gray-500 mb-2">Algorithm Integration</div>
                            <p class="text-gray-700">集成多种医学评估算法，提供科学准确的健康风险评估</p>
                        </div>
                    </div>
                </div>

                <!-- 技术收获 -->
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4 title-black">技术收获与挑战</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="gradient-card-4 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-code text-3xl icon-risk mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">模块化设计</h4>
                            <p class="text-sm text-gray-600">掌握了大型C程序的模块化架构设计</p>
                        </div>
                        <div class="gradient-card-5 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-file-csv text-3xl icon-tech mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">文件处理</h4>
                            <p class="text-sm text-gray-600">实现了复杂的CSV文件读写与数据持久化</p>
                        </div>
                        <div class="gradient-card-6 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-calculator text-3xl icon-innovation mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">算法实现</h4>
                            <p class="text-sm text-gray-600">将医学评估模型转化为程序算法</p>
                        </div>
                        <div class="gradient-card-1 rounded-lg p-6 border border-gray-200 shadow-sm text-center">
                            <i class="fas fa-bug text-3xl icon-health mb-3"></i>
                            <h4 class="text-lg font-semibold mb-2 title-black">调试优化</h4>
                            <p class="text-sm text-gray-600">提升了程序调试与性能优化能力</p>
                        </div>
                    </div>
                </div>

                <!-- 展望与改进 -->
                <div>
                    <h3 class="text-2xl font-semibold mb-4 title-black">展望与改进方向</h3>
                    <div class="mermaid">
                        graph LR
                        A[当前系统] --> B[功能扩展]
                        A --> C[技术升级]
                        A --> D[用户体验]

                        B --> B1[增加更多专业健康指标]
                        B --> B2[机器学习预测]
                        B1 -->B11[提升系统专业性]
                        B2-->B11

                        C --> C1[增加管理员和用户角色]
                        C1 --> C11[提高系统安全性]
                        C1 --> C12[数据加密]


                        D --> D1[界面美化]
                        D --> D2[操作简化]
                        D --> D3[多语言支持]
                    </div>
                </div>
            </div>
        </section>

        <!-- 参考文献 -->
        <section class="mb-16 fade-in">
            <div class="card-bg rounded-xl p-8 border border-gray-200 shadow-lg">
                <h2 class="text-4xl font-bold title-black mb-8 flex items-center">
                    <i class="fas fa-book mr-4 icon-medical"></i>参考文献
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="gradient-card-2 rounded-lg p-6 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold mb-3 title-black">医学标准与指南</h4>
                        <ul class="space-y-2 text-sm text-gray-700">
                            <li>• 中国心血管病预防指南(2017)</li>
                            <li>• 中国2型糖尿病防治指南(2020版)</li>
                            <li>• 中国慢性肾脏病诊治指南</li>
                            <li>• WHO全球健康风险评估标准</li>
                        </ul>
                    </div>
                    <div class="gradient-card-3 rounded-lg p-6 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold mb-3 title-black">技术文档与资料</h4>
                        <ul class="space-y-2 text-sm text-gray-700">
                            <li>• C Programming Language (K&R)</li>
                            <li>• 数据结构与算法分析</li>
                            <li>• 软件工程实践指南</li>
                            <li>• 医疗信息系统设计原理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统演示 -->
        <section class="mb-16 fade-in">
            <div class="card-bg rounded-xl p-8 border border-gray-200 shadow-lg">
                <h2 class="text-4xl font-bold title-black mb-8 flex items-center">
                    <i class="fas fa-desktop mr-4 icon-system"></i>系统功能演示
                </h2>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 主要功能流程 -->
                    <div class="gradient-card-4 rounded-lg p-6 border border-gray-200 shadow-sm">
                        <h4 class="text-xl font-semibold mb-4 title-black">系统操作流程</h4>
                        <div class="mermaid">
                            sequenceDiagram
                            participant U as 用户
                            participant S as 系统
                            participant D as 数据库

                            U->>S: 启动程序
                            S->>D: 加载历史数据
                            D-->>S: 返回数据
                            S-->>U: 显示主菜单

                            U->>S: 选择功能
                            alt 数据录入
                            S->>U: 显示输入界面
                            U->>S: 输入健康数据
                            S->>D: 保存数据
                            else 健康评估
                            S->>D: 获取最新数据
                            S->>S: 执行评估算法
                            S-->>U: 显示评估结果
                            else 风险评估
                            S->>S: 执行风险算法
                            S-->>U: 显示风险报告
                            end
                        </div>
                    </div>

                    <!-- 数据处理架构 -->
                    <div class="gradient-card-5 rounded-lg p-6 border border-gray-200 shadow-sm">
                        <h4 class="text-xl font-semibold mb-4 title-black">数据处理架构</h4>
                        <div class="mermaid">
                            graph TD
                            A[用户输入] --> B[数据验证]
                            B --> C[格式转换]
                            C --> D[内存存储]
                            D --> E[算法处理]
                            E --> F[结果生成]
                            F --> G[文件保存]

                            D --> H[CSV导出]
                            D --> I[数据查询]
                            D --> J[趋势分析]

                            style A fill:#e1f5fe
                            style F fill:#e8f5e8
                            style G fill:#fff3e0
                        </div>
                    </div>
                </div>

                <!-- 核心数据结构 -->
                <div class="mt-8">
                    <h4 class="text-xl font-semibold mb-4 title-black">核心数据结构设计</h4>
                    <div class="gradient-card-6 rounded-lg p-6 border border-gray-200 shadow-sm">
                        <div class="mermaid">
                            classDiagram
                            class HealthData {
                            +int age
                            +int gender
                            +float height
                            +float weight
                            +float systolic_bp
                            +float diastolic_bp
                            +float fasting_glucose
                            +float hba1c
                            +float cholesterol
                            +float ldl
                            +float creatinine
                            +int is_smoker
                            +int family_history
                            }

                            class HealthRecord {
                            +char date[20]
                            +char name[50]
                            +HealthData data
                            +int heartRate
                            +float sleepHours
                            }

                            class RiskResult {
                            +int cvd_risk_level
                            +int diabetes_risk
                            +int ckd_risk
                            +int oncology_alert
                            +char advice[512]
                            }

                            HealthRecord --> HealthData
                            HealthData --> RiskResult : 评估生成
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- 页脚 -->
    <footer class="tech-gradient text-white py-8 text-center">
        <div class="container mx-auto px-6">
            <p class="text-lg mb-2">个人健康数据管理系统</p>
            <p class="text-sm opacity-80">基于C语言开发 | 智能健康管理解决方案</p>
            <div class="mt-4 flex justify-center space-x-6">
                <i class="fab fa-github text-2xl opacity-70 hover:opacity-100 cursor-pointer"></i>
                <i class="fas fa-envelope text-2xl opacity-70 hover:opacity-100 cursor-pointer"></i>
                <i class="fas fa-globe text-2xl opacity-70 hover:opacity-100 cursor-pointer"></i>
            </div>
        </div>
    </footer>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#00AEEF',
                primaryTextColor: '#333',
                primaryBorderColor: '#00AEEF',
                lineColor: '#00AEEF'
            }
        });

        // 初始化语法高亮
        hljs.highlightAll();

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>

</html>