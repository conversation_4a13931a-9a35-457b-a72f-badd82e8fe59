
#include "define.h"

void Convert_Data(const HealthRecord *record, HealthData *data)
{
    if (record == NULL || data == NULL)
    {
        return; // 处理空指针情况
    }

    data->age = record->age;                         // 年龄
    data->gender = record->gender;                   // 性别
    data->height = record->height / 100.0f;          // cm → m，身高
    data->weight = record->weight;                   // 体重
    data->systolic_bp = record->systolic_bp;         // 收缩压
    data->diastolic_bp = record->diastolic_bp;       // 舒张压
    data->fasting_glucose = record->fasting_glucose; // 空腹血糖
    data->hba1c = record->hba1c;                     // HbA1c
    data->cholesterol = record->cholesterol;         // 总胆固醇
    data->ldl = record->ldl;                         // 低密度脂蛋白胆固醇
    data->creatinine = record->creatinine;           // 血清肌酐
    data->is_smoker = record->is_smoker;             // 是否吸烟
    data->family_history = record->family_history;   // 家族史
    data->lp_a = record->lp_a * 38.67f;              // mmol/L → mg/dL，脂蛋白(a)
    data->hs_crp = record->hs_crp;                   // 高灵敏度C反应蛋白
}

// 获取最新的健康记录
HealthRecord *getLatestRecord(HealthRecord records[], int numRecords)
{
    // 如果记录数量小于等于0或者记录数组为空，则返回NULL
    if (numRecords <= 0 || records == NULL)
    {
        return NULL; // 安全返回NULL，调用方需要检查
    }
    // 返回最新的健康记录
    return &records[numRecords - 1];
}

void print_post_action_prompt()
{
    printf("\n稍等片刻后请按Enter键继续...");
    while (getchar() != '\n')
        ;      // 清空输入缓冲区
    getchar(); // 等待用户按Enter
}

void print_no_data_warning(const char *operation)
{
    printf("无%s数据！\n", operation);
}

void print_operation_header(const char *operation)
{
    printf("\n=== %s ===\n", operation);
}
