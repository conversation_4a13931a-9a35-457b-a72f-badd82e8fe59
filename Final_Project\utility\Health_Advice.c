
#include "define.h"

// ========== 辅助函数 ==========
int get_bmi_category(float height, float weight)
{
    if (height == NO_INPUT_FLOAT || weight == NO_INPUT_FLOAT)
        return -1; // 数据不完整

    float bmi = weight / (height * height);
    if (bmi < 18.5)
        return 0; // 偏瘦
    else if (bmi < 24)
        return 1; // 正常
    else if (bmi < 28)
        return 2; // 超重
    else
        return 3; // 肥胖
}

int get_bp_category(float systolic, float diastolic)
{
    if (systolic == NO_INPUT_FLOAT || diastolic == NO_INPUT_FLOAT)
        return -1; // 数据不完整

    if (systolic < 120 && diastolic < 80)
        return 0; // 正常
    else if (systolic < 130 && diastolic < 85)
        return 1; // 正常高值
    else if (systolic < 140 && diastolic < 90)
        return 2; // 临界高血压
    else
        return 3; // 高血压
}

float calculate_egfr(int age, int gender, float creatinine)
{
    if (age == NO_INPUT_INT || gender == NO_INPUT_INT || creatinine == NO_INPUT_FLOAT)
        return NO_INPUT_FLOAT;

    float kappa = (gender == 1) ? 0.9 : 0.7;
    float alpha = (gender == 1) ? -0.302 : -0.241;
    float min_cr = fmin(creatinine / kappa, 1.0);
    float max_cr = fmax(creatinine / kappa, 1.0);

    float egfr = 141 * pow(min_cr, alpha) * pow(max_cr, -1.209) * pow(0.993, age);
    if (gender == 0)
        egfr *= 1.018;
    return egfr;
}

// ========== 健康建议生成 ==========
HealthAdvice generate_targeted_advice(HealthData data)
{
    HealthAdvice advice = {0};
    // char temp_summary[1024] = "";
    bool has_any_advice = false;

    // 1. BMI建议 (需要身高和体重数据)
    if (data.height != NO_INPUT_FLOAT && data.weight != NO_INPUT_FLOAT)
    {
        int bmi_cat = get_bmi_category(data.height, data.weight);
        if (bmi_cat == 0)
        {
            snprintf(advice.weight, sizeof(advice.weight),
                     "【体重偏瘦】\n"
                     "• 增加优质蛋白摄入：每天1-2个鸡蛋、200ml牛奶\n"
                     "• 适量健康脂肪：坚果20-30g/天，橄榄油10-15ml/天");
            has_any_advice = true;
        }
        else if (bmi_cat >= 2)
        {
            snprintf(advice.weight, sizeof(advice.weight),
                     "【体重%s】\n"
                     "• 控制总热量：每日减少300-500kcal\n"
                     "• 增加膳食纤维：每日蔬菜500g以上\n"
                     "• 运动：每周150分钟中等强度运动",
                     (bmi_cat == 2) ? "超重" : "肥胖");
            has_any_advice = true;
        }
    }

    // 2. 血压建议 (需要收缩压和舒张压数据)
    if (data.systolic_bp != NO_INPUT_FLOAT && data.diastolic_bp != NO_INPUT_FLOAT)
    {
        int bp_cat = get_bp_category(data.systolic_bp, data.diastolic_bp);
        if (bp_cat >= 1)
        {
            snprintf(advice.blood_pressure, sizeof(advice.blood_pressure),
                     "【血压%s】\n"
                     "• 限盐：每日<5g\n"
                     "• 运动：每周5次30分钟有氧\n"
                     "• 监测：每周测量2-3次",
                     (bp_cat == 1) ? "正常高值" : (bp_cat == 2) ? "临界高血压"
                                                                : "高血压");
            has_any_advice = true;
        }
    }

    // 3. 血糖建议 (需要空腹血糖或糖化血红蛋白数据)
    bool has_glucose = data.fasting_glucose != NO_INPUT_FLOAT;
    bool has_hba1c = data.hba1c != NO_INPUT_FLOAT;

    if (has_glucose || has_hba1c)
    {
        bool is_diabetic = (data.fasting_glucose >= 7.0f) || (data.hba1c >= 6.5f);
        bool is_prediabetic = (data.fasting_glucose >= 6.1f) || (data.hba1c >= 5.7f);

        if (is_diabetic || is_prediabetic)
        {
            snprintf(advice.blood_sugar, sizeof(advice.blood_sugar),
                     "【血糖%s】\n"
                     "• 饮食：低GI食物，控制碳水\n"
                     "• 运动：餐后1小时运动30分钟\n"
                     "• 监测：%s",
                     is_diabetic ? "异常(糖尿病)" : "异常(糖尿病前期)",
                     is_diabetic ? "每周2-4次血糖检测" : "每3-6个月复查");
            has_any_advice = true;
        }
    }

    // 4. 血脂建议 (需要LDL或总胆固醇数据)
    bool has_ldl = data.ldl != NO_INPUT_FLOAT;
    bool has_chol = data.cholesterol != NO_INPUT_FLOAT;

    if (has_ldl || has_chol)
    {
        bool high_ldl = has_ldl && data.ldl >= 3.4f;
        bool high_chol = has_chol && data.cholesterol >= 5.2f;

        if (high_ldl || high_chol)
        {
            snprintf(advice.blood_lipid, sizeof(advice.blood_lipid),
                     "【血脂异常】\n"
                     "• 饮食：减少饱和脂肪，增加ω-3\n"
                     "• 运动：每周5次40分钟有氧\n"
                     "• 控制体重：BMI<24");
            has_any_advice = true;
        }
    }

    // 5. 肾功能建议 (需要肌酐、年龄和性别数据)
    if (data.creatinine != NO_INPUT_FLOAT && data.age != NO_INPUT_INT && data.gender != NO_INPUT_INT)
    {
        float egfr = calculate_egfr(data.age, data.gender, data.creatinine);
        if (egfr < 90)
        {
            snprintf(advice.kidney, sizeof(advice.kidney),
                     "【肾功能%s】\n"
                     "• 控制蛋白质：0.6-0.8g/kg/天\n"
                     "• 血压：目标<130/80mmHg\n"
                     "• 监测：%s",
                     (egfr < 60) ? "中度下降" : "轻度下降",
                     (egfr < 60) ? "每3个月复查" : "每年复查");
            has_any_advice = true;
        }
    }

    // 6. 生活方式建议
    if (data.is_smoker == 1)
    {
        snprintf(advice.smoking, sizeof(advice.smoking),
                 "【吸烟】\n"
                 "• 戒烟计划：逐步减少吸烟量\n"
                 "• 替代疗法：尼古丁口香糖/贴片\n"
                 "• 寻求专业帮助");
        has_any_advice = true;
    }

    // 7. 炎症建议 (需要hs-CRP数据)
    if (data.hs_crp != NO_INPUT_FLOAT && data.hs_crp >= 2.0f)
    {
        snprintf(advice.inflammation, sizeof(advice.inflammation),
                 "【炎症状态】\n"
                 "• 抗炎饮食：深色蔬菜、浆果\n"
                 "• 规律运动：中等强度有氧\n"
                 "• 治疗感染灶：如牙周炎");
        has_any_advice = true;
    }

    // 构建总结建议
    if (has_any_advice)
    {
        const char *sections[] = {
            advice.weight, advice.blood_pressure, advice.blood_sugar,
            advice.blood_lipid, advice.kidney, advice.smoking,
            advice.inflammation};

        snprintf(advice.summary, sizeof(advice.summary),
                 COLOR_BOLD "❗ 基于可用数据的健康建议 ❗\n\n" COLOR_RESET);

        for (int i = 0; i < sizeof(sections) / sizeof(sections[0]); i++)
        {
            if (sections[i][0] != '\0')
            {
                strcat(advice.summary, sections[i]);
                strcat(advice.summary, "\n\n");
            }
        }

        strcat(advice.summary,
               COLOR_GRAY "注：建议执行3个月后复查相关指标\n"
                          "未评估项目可能因数据不足" COLOR_RESET);
    }
    else
    {
        snprintf(advice.summary, sizeof(advice.summary),
                 COLOR_SUCCESS "✓ 基于可用数据未发现明显健康问题\n"
                               "继续保持健康生活方式，定期体检" COLOR_RESET);
    }

    return advice;
}

// ========== 风险建议生成 ==========
void generate_cvd_advice(int risk_level, char *advice_buffer)
{
    if (risk_level == NO_INPUT_INT)
        return;

    const char *level_str =
        (risk_level == 3) ? "极高危" : (risk_level == 2) ? "高危"
                                                         : "中危";

    sprintf(advice_buffer + strlen(advice_buffer),
            "[心脑血管%s风险]\n", level_str);

    if (risk_level >= 2)
    {
        strcat(advice_buffer,
               "• 立即心血管专科评估\n"
               "• 强化降脂目标：LDL-C<1.8mmol/L\n"
               "• 血压控制：目标<130/80mmHg\n");
    }
    else
    {
        strcat(advice_buffer,
               "• 生活方式干预为主\n"
               "• 控制体重、血压、血糖、血脂\n"
               "• 每年全面体检一次\n");
    }
}

void generate_diabetes_advice(int risk_level, char *advice_buffer)
{
    if (risk_level == NO_INPUT_INT)
        return;

    const char *level_str =
        (risk_level == 3) ? "极高危" : (risk_level == 2) ? "高危"
                                                         : "中危";

    sprintf(advice_buffer + strlen(advice_buffer),
            "[糖尿病%s风险]\n", level_str);

    strcat(advice_buffer,
           (risk_level >= 2) ? "• 3个月内OGTT检查\n"
                               "• 减重目标：超重者减重7-10%\n"
                             : "• 每年筛查血糖指标\n"
                               "• 控制BMI<24\n");
}

void generate_ckd_advice(int risk_level, char *advice_buffer)
{
    if (risk_level == NO_INPUT_INT)
        return;

    const char *level_str =
        (risk_level >= 3) ? "重度风险" : (risk_level == 2) ? "中度风险"
                                                           : "轻度风险";

    sprintf(advice_buffer + strlen(advice_buffer),
            "[慢性肾病%s]\n", level_str);

    strcat(advice_buffer,
           (risk_level >= 3) ? "• 立即肾内科就诊\n"
                               "• 严格血压控制：<130/80mmHg\n"
                             : "• 控制危险因素\n"
                               "• 每年检查肾功能\n");
}

void generate_oncology_advice(int risk_level, char *advice_buffer)
{
    if (risk_level == NO_INPUT_INT)
        return;

    const char *level_str = (risk_level == 2) ? "高风险" : "中等风险";

    sprintf(advice_buffer + strlen(advice_buffer),
            "[肿瘤%s]\n", level_str);

    strcat(advice_buffer,
           (risk_level == 2) ? "• 低剂量胸部CT(吸烟者)\n"
                               "• 胃肠镜检查\n"
                             : "• 基础癌症筛查\n"
                               "• 改善生活方式\n");
}

void generate_risk_advice(const RiskResult *risk, char *advice_buffer)
{
    if (!risk)
        return;

    advice_buffer[0] = '\0';
    bool has_any_risk = false;

    if (risk->cvd_risk_level != NO_INPUT_INT && risk->cvd_risk_level >= 1)
    {
        generate_cvd_advice(risk->cvd_risk_level, advice_buffer);
        has_any_risk = true;
    }

    if (risk->diabetes_risk != NO_INPUT_INT && risk->diabetes_risk >= 1)
    {
        generate_diabetes_advice(risk->diabetes_risk, advice_buffer);
        has_any_risk = true;
    }

    if (risk->ckd_risk != NO_INPUT_INT && risk->ckd_risk >= 1)
    {
        generate_ckd_advice(risk->ckd_risk, advice_buffer);
        has_any_risk = true;
    }

    if (risk->oncology_alert != NO_INPUT_INT && risk->oncology_alert >= 1)
    {
        generate_oncology_advice(risk->oncology_alert, advice_buffer);
        has_any_risk = true;
    }

    if (!has_any_risk)
    {
        strcpy(advice_buffer,
               "✓ 基于可用数据未发现明显慢性病风险\n"
               "继续保持健康生活方式，定期体检\n");
    }
}

void print_health_advice(const HealthAdvice *advice)
{
    if (!advice)
        return;

    printf("\n" COLOR_BOLD "======== 健康建议 ========" COLOR_RESET "\n");
    printf("%s\n", advice->summary);
    printf(COLOR_GRAY "==========================" COLOR_RESET "\n");
}
