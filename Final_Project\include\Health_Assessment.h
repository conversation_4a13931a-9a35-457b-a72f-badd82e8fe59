#ifndef __HEALTH_ASSESSMENT_H
#define __HEALTH_ASSESSMENT_H

#include "define.h"

// // 健康评估结果
// typedef struct
// {
//     float physical_score;       // 躯体健康分数
//     float mental_score;         // 心理健康分数
//     float total_score;          // 综合评分
//     char physical_comment[100]; // 躯体健康评语
//     char mental_comment[100];   // 心理健康评语
//     char overall_rating[50];    // 综合评级
// } HealthAssessment;
// 在Health_Assessment.h中修改结构体
typedef struct
{
    // 各维度评估结果
    struct
    {
        bool has_data;
        float score;
        char comment[100];
    } bmi, blood_pressure, blood_sugar, blood_lipid, kidney, lifestyle, inflammation;

    // 综合评估
    float total_score;
    char overall_rating[50];
} HealthAssessment;

/**
 * @brief BMI评估（体重）
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_bmi(HealthData data, float *score, char *comment);

/**
 * @brief 血压评估
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_blood_pressure(HealthData data, float *score, char *comment);

/**
 * @brief 血糖评估（包含空腹血糖和糖化血红蛋白）
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_blood_sugar(HealthData data, float *score, char *comment);

/**
 * @brief 血脂评估（包含LDL-C和总胆固醇）
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_blood_lipid(HealthData data, float *score, char *comment);

/**
 * @brief 肾功能评估（基于eGFR计算）
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_kidney(HealthData data, float *score, char *comment);

/**
 * @brief 生活方式评估（吸烟和家族史）
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_lifestyle(HealthData data, float *score, char *comment);

/**
 * @brief 炎症评估（脂蛋白(a)和超敏C反应蛋白）
 * @param data 健康数据
 * @param score 评估分数输出指针
 * @param comment 评估说明输出指针
 * @return true评估成功 false数据不足
 */
bool assess_inflammation(HealthData data, float *score, char *comment);

/**
 * @brief 综合健康评估
 * @param data 健康数据
 * @return HealthAssessment结构体，包含所有维度的评估结果
 */
HealthAssessment evaluate_health(HealthData data);

/**
 * @brief 打印健康评估报告
 * @param assessment 要打印的评估结果
 */
void print_health_report(HealthAssessment assessment);

/**
 * @brief 执行完整健康评估流程
 * @param data 健康数据
 * @return 生成的健康评估报告
 */
HealthAssessment perform_health_evaluation(HealthData data);

/**
 * @brief 提供针对性健康建议
 * @param data 健康数据
 */
void provide_targeted_advice(HealthData data);

#endif //__HEALTH_ASSESSMENT_H