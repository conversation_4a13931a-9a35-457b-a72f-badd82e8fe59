#ifndef __DEFINE_H
#define __DEFINE_H

#include <stdbool.h>  // 布尔类型
#include <stdio.h>    // 标准输入输出库
#include <stdlib.h>   // 标准库
#include <string.h>   // 字符串处理库
#include <math.h>     // 数学库
#include <sys/stat.h> // 包含系统状态头文件  // 目录操作
#include <time.h>     // 时间库
#include <ctype.h>    // 字符处理库
#include <direct.h>   // 目录操作
#include <errno.h>    // 错误号
#include <dirent.h>   // 包含目录操作的头文件

#include "Include_HeadFile.h"
#include "Import_Data.h"
#include "Health_Advice.h"
#include "Export_File_Data.h"
#include "Health_Assessment.h"
#include "Import_File_Data.h"
#include "Menu.h"
#include "Query_HealthData.h"
#include "Risk_Result.h"
#include "Trend_Alert.h"
#include "Convert_Data.h"
#include "Get_Check_Path.h"

// 颜色代码宏定义
#define COLOR_RESET "\033[0m"
#define COLOR_RED "\033[31m"
#define COLOR_BOLD_RED "\033[1;31m"
#define COLOR_GREEN "\033[32m"
#define COLOR_YELLOW "\033[33m"
#define COLOR_BLUE "\033[34m"
#define COLOR_MAGENTA "\033[35m"
#define COLOR_CYAN "\033[36m"
#define COLOR_GRAY "\033[2m"
#define COLOR_BOLD "\033[1m"
// #define COLOR_HIGHLIGHT "\033[1;33m" // 加亮黄色
#define COLOR_WARNING "\033[1;33m" // 警告色（黄色）
#define COLOR_ERROR "\033[1;31m"   // 错误色（红色）
#define COLOR_INFO "\033[36m"      // 信息色（青色）
#define COLOR_SUCCESS "\033[32m"   // 成功色（绿色）

#define MAX_PATH 260    // 最大路径长度
#define MAX_ENTRIES 100 // 最大记录条数
// 定义未输入数据的标记值
#define NO_INPUT_FLOAT -9999.0f
#define NO_INPUT_INT -9999

// 声明一个全局变量，用于存储健康记录
extern HealthRecord records[MAX_ENTRIES];
// 声明一个全局变量，用于存储健康记录的数量
extern int numRecords;
// 声明全局变量，记录是否有新数据录入
extern int hasNewData;

#endif //__DEFINE_H