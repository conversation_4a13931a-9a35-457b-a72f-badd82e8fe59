
#include "define.h"

void saveToFile(HealthRecord records[], int numRecords)
{
    extern int hasNewData;
    static int isSavingInProgress = 0;

    // 如果没有新数据，直接返回
    if (!hasNewData)
    {
        printf("[提示] 没有新数据需要保存\n");
        return;
    }

    // 1. 检查是否已有保存操作在进行
    if (isSavingInProgress)
    {
        printf("[警告] 保存操作正在进行中，忽略本次请求\n");
        return;
    }
    isSavingInProgress = 1;

    // 2. 检查是否有有效记录
    HealthRecord *latest = getLatestRecord(records, numRecords);
    if (latest == NULL)
    {
        printf("[错误] 没有可保存的健康记录\n");
        isSavingInProgress = 0;
        return;
    }

    // 3. 确保目录存在
    struct stat dir_stat;
    if (stat(getSaveDir(), &dir_stat) == -1)
    {
        if (_mkdir(getSaveDir()) == -1)
        {
            perror("[错误] 创建目录失败");
            isSavingInProgress = 0;
            return;
        }
    }

    // 4. 生成带时间戳的文件名
    time_t now = time(NULL);
    struct tm *timeinfo = localtime(&now);
    char filename[MAX_PATH];
    snprintf(filename, MAX_PATH, "%s/%04d%02d%02d_%02d%02d%02d.csv",
             getSaveDir(),
             timeinfo->tm_year + 1900,
             timeinfo->tm_mon + 1,
             timeinfo->tm_mday,
             timeinfo->tm_hour,
             timeinfo->tm_min,
             timeinfo->tm_sec);

    // 5. 写入文件
    FILE *file = fopen(filename, "w");
    if (!file)
    {
        printf("[错误] 无法创建文件 %s: %s\n", filename, strerror(errno));
        isSavingInProgress = 0;
        return;
    }

    // 写入CSV表头
    fprintf(file, "日期,姓名,年龄,性别,身高(cm),体重(kg),收缩压,舒张压,空腹血糖,糖化血红蛋白,总胆固醇,LDL,肌酐,心率,睡眠时长,是否吸烟,家族史,脂蛋白A,超敏C反应蛋白\n");

    // 写入记录
    int successCount = 0;
    for (int i = 0; i < numRecords; i++)
    {
        int len = fprintf(file, "%s,%s,%d,%d,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%.1f,%d,%.1f,%d,%d,%.1f,%.1f\n",
                          records[i].date, records[i].name, records[i].age, records[i].gender,
                          records[i].height, records[i].weight,
                          records[i].systolic_bp, records[i].diastolic_bp,
                          records[i].fasting_glucose, records[i].hba1c,
                          records[i].cholesterol, records[i].ldl,
                          records[i].creatinine, records[i].heartRate,
                          records[i].sleepHours, records[i].is_smoker,
                          records[i].family_history, records[i].lp_a,
                          records[i].hs_crp);

        if (len > 0)
            successCount++;
    }
    fclose(file);
    isSavingInProgress = 0;

    // 6. 输出结果
    if (successCount > 0)
    {
        printf("[成功] 保存 %d/%d 条记录到 %s\n", successCount, numRecords, filename);
        printf("数据已保存为UTF-8编码的CSV文件\n");
        hasNewData = 0; // 重置新数据标志
    }
    else
    {
        printf("[错误] 所有记录保存失败\n");
        remove(filename); // 删除空文件
    }
}