#ifndef __TREND_ALERT_H
#define __TREND_ALERT_H

#include "define.h"

// 趋势分析结果结构体
typedef struct
{
    char indicator[30];       // 指标名称
    float current_value;      // 当前值
    float prev_value;         // 前次值
    float change_percent;     // 变化百分比
    char trend_status[20];    // 趋势状态
    int alert_level;          // 预警等级：0=正常，1=关注，2=警告
    char recommendation[256]; // 建议
} TrendAlert;

/**
 * @brief 初始化TrendAlert结构体
 * @param alert 要初始化的TrendAlert指针
 * @param indicator 指标名称
 * @param current 当前值
 * @param prev 前次值
 * @param change 变化百分比
 */
void initTrendAlert(TrendAlert *alert, const char *indicator,
                    float current, float prev, float change);
/**
 * @brief 计算两个日期之间的天数差
 * @param date1 起始日期(YYYY-MM-DD格式)
 * @param date2 结束日期(YYYY-MM-DD格式)
 * @return 天数差
 */
int daysBetween(const char *date1, const char *date2);

/**
 * @brief 检查数据值是否有效
 * @param value 要检查的值
 * @return true有效 false无效
 */
bool is_valid_value(float value);

/**
 * @brief 获取有效的健康数据点
 * @param values 原始数值数组
 * @param dates 原始日期数组
 * @param count 数据数量
 * @param valid_values 有效数值存储数组
 * @param valid_dates 有效日期存储数组
 * @return 有效数据点数量
 */
int get_valid_data_points(float *values, char *dates[], int count,
                          float *valid_values, char **valid_dates);

/**
 * @brief 分析健康指标趋势
 * @param indicator 指标名称
 * @param values 数值数组
 * @param dates 日期数组
 * @param count 数据数量
 * @param alert 存储分析结果的TrendAlert指针
 */
void analyze_trend(const char *indicator, float *values, char *dates[], int count,
                   TrendAlert *alert);

/**
 * @brief 生成趋势警报
 * @param records 健康记录数组
 * @param numRecords 记录数量
 * @param alerts 警报存储数组
 * @param alert_count 警报计数指针
 */
void generate_trend_alert(const HealthRecord records[], int numRecords,
                          TrendAlert *alerts, int *alert_count);
/**
 * @brief 打印趋势分析报告
 * @param alerts 警报数组
 * @param alert_count 警报数量
 * @param records 健康记录数组
 * @param numRecords 记录数量
 */
void print_trend_report(const TrendAlert *alerts, int alert_count,
                        const HealthRecord records[], int numRecords);

/**
 * @brief 打印健康评估报告
 * @param records 健康记录数组
 * @param numRecords 记录数量
 */
void print_health_assessment(const HealthRecord records[], int numRecords);

/**
 * @brief 分析心脑血管疾病风险趋势
 * @param records 健康记录数组
 * @param numRecords 记录数量
 * @param alerts 警报存储数组
 * @param alert_count 警报计数指针
 */
void analyze_cvd_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count);

/**
 * @brief 分析糖尿病风险趋势
 * @param records 健康记录数组
 * @param numRecords 记录数量
 * @param alerts 警报存储数组
 * @param alert_count 警报计数指针
 */
void analyze_diabetes_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count);

/**
 * @brief 分析慢性肾病风险趋势
 * @param records 健康记录数组
 * @param numRecords 记录数量
 * @param alerts 警报存储数组
 * @param alert_count 警报计数指针
 */
void analyze_ckd_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count);

/**
 * @brief 分析肿瘤风险趋势
 * @param records 健康记录数组
 * @param numRecords 记录数量
 * @param alerts 警报存储数组
 * @param alert_count 警报计数指针
 */
void analyze_oncology_trend(const HealthRecord records[], int numRecords, TrendAlert *alerts, int *alert_count);

/**
 * @brief 综合慢性病趋势分析
 * @param records 健康记录数组
 * @param numRecords 记录数量
 * @param alerts 警报存储数组
 * @param alert_count 警报计数指针
 */
void analyze_chronic_disease_trends(const HealthRecord records[], int numRecords,
                                    TrendAlert *alerts, int *alert_count);
/**
 * @brief 打印慢性病趋势分析报告
 * @param alerts 警报数组
 * @param alert_count 警报数量
 * @param records 健康记录数组
 * @param numRecords 记录数量
 */
void print_chronic_trend_report(const TrendAlert *alerts, int alert_count,
                                const HealthRecord records[], int numRecords);
/**
 * @brief 打印单个趋势警报
 * @param alert 要打印的警报指针
 */
void print_single_alert(const TrendAlert *alert);

#endif // __TREND_ALERT_H