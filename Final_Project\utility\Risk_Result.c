
#include "define.h"

// 心脑血管疾病风险评估 (China-PAR模型增强版)
int assess_cvd_risk(HealthData data)
{
    // 基础风险因子计算
    float bmi = data.weight / (data.height * data.height);
    int score = 0;

    // 1. 人口统计学因素
    // 年龄评分（非线性增长）
    if (data.age >= 75)
        score += 8;
    else if (data.age >= 65)
        score += 6;
    else if (data.age >= 55)
        score += 4;
    else if (data.age >= 45)
        score += 2;

    // 性别差异（女性绝经后风险增加）
    if (data.gender == 1)
    {
        // 男性风险
        if (data.age >= 45)
            score += 2;
    }
    else
    {
        // 女性风险（绝经后）
        if (data.age >= 55)
            score += 3;
    }

    // 2. 血压分级（考虑高血压分级）
    if (data.systolic_bp >= 180 || data.diastolic_bp >= 110)
    {
        score += 8; // 3级高血压
    }
    else if (data.systolic_bp >= 160 || data.diastolic_bp >= 100)
    {
        score += 6; // 2级高血压
    }
    else if (data.systolic_bp >= 140 || data.diastolic_bp >= 90)
    {
        score += 4; // 1级高血压
    }
    else if (data.systolic_bp >= 130)
    {
        score += 2; // 正常高值
    }

    // 3. 代谢因素
    // BMI评分（亚洲标准）
    if (bmi >= 28.0)
        score += 4; // 肥胖
    else if (bmi >= 24.0)
        score += 2; // 超重

    // 血脂异常 (考虑多重危险因素)
    if (data.ldl >= 4.9)
        score += 5; // 极高LDL-C
    else if (data.ldl >= 4.1)
        score += 3;
    else if (data.ldl >= 3.4)
        score += 1;

    // 非HDL-C风险
    if (data.cholesterol != NO_INPUT_FLOAT && data.ldl != NO_INPUT_FLOAT)
    {
        float non_hdl = data.cholesterol - data.ldl;
        if (non_hdl >= 4.1)
            score += 2;
    }

    // 4. 血糖代谢
    if (data.fasting_glucose >= 7.0)
        score += 4; // 糖尿病标准
    else if (data.fasting_glucose >= 6.1)
        score += 2; // IFG

    if (data.hba1c >= 6.5)
        score += 3;
    else if (data.hba1c >= 5.7)
        score += 1;

    // 5. 肾功能
    float egfr = calculate_egfr(data.age, data.gender, data.creatinine);
    if (egfr < 45)
        score += 4; // CKD 3b期及以上
    else if (egfr < 60)
        score += 2; // CKD 3a期

    // 6. 行为与遗传因素
    // 吸烟（考虑吸烟量）
    if (data.is_smoker)
        score += 3;

    // 家族史（考虑早发心血管病家族史）
    if (data.family_history)
    {
        if (data.age < 55 && data.gender == 1)
            score += 3; // 男性早发家族史
        else if (data.age < 65 && data.gender == 0)
            score += 3; // 女性早发家族史
        else
            score += 2;
    }

    // 7. 新型生物标志物
    if (data.lp_a > 50)
        score += 2; // Lp(a)>50mg/dL高风险
    if (data.hs_crp >= 3.0)
        score += 3; // 显著炎症状态
    else if (data.hs_crp >= 2.0)
        score += 1;

    // 风险分级（更精细的分类）
    if (score >= 15)
        return 3; // 极高危：10年风险≥20%
    else if (score >= 10)
        return 2; // 高危：10年风险10-19%
    else if (score >= 5)
        return 1; // 中危：5-9%
    else
        return 0; // 低危：<5%
}

// 糖尿病风险评估 (ADA标准增强版)
int assess_diabetes_risk(HealthData data)
{
    float bmi = data.weight / (data.height * data.height);
    int score = 0;

    // 1. 人口统计学因素
    if (data.age >= 65)
        score += 3;
    else if (data.age >= 45)
        score += 2;

    // 性别差异（GDM史等未包含在基础数据中）
    if (data.gender == 0)
        score += 1; // 女性略高风险

    // 2. 体重因素
    if (bmi >= 28.0)
        score += 4; // 肥胖
    else if (bmi >= 24.0)
        score += 2; // 超重

    // 3. 血糖代谢
    if (data.fasting_glucose >= 7.0)
        score += 8; // 达到糖尿病标准
    else if (data.fasting_glucose >= 6.1)
        score += 5; // IFG

    if (data.hba1c >= 6.5)
        score += 6;
    else if (data.hba1c >= 5.7)
        score += 3;

    // 4. 血压因素
    if (data.systolic_bp >= 140 || data.diastolic_bp >= 90)
        score += 2;

    // 5. 血脂因素
    if (data.ldl >= 3.4)
        score += 1;
    if (data.cholesterol >= 5.2)
        score += 1;

    // 6. 其他风险因素
    if (data.family_history)
        score += 3;
    if (data.is_smoker)
        score += 2;

    // 风险分级（基于10年风险）
    if (score >= 15)
        return 3; // 极高危：10年风险≥50%
    else if (score >= 10)
        return 2; // 高危：20-49%
    else if (score >= 5)
        return 1; // 中危：5-19%
    else
        return 0; // 低危：<5%
}

// 慢性肾病风险评估 (KDIGO指南增强版)
int assess_ckd_risk(HealthData data)
{
    // eGFR计算（CKD-EPI公式，比MDRD更准确）
    float egfr = calculate_egfr(data.age, data.gender, data.creatinine);

    // 蛋白尿评估（简化版，实际应检测ACR）
    int proteinuria_risk = 0;
    if (data.creatinine < 100 && egfr < 90)
        proteinuria_risk = 1; // 疑似蛋白尿
    if (data.creatinine >= 100 && egfr < 60)
        proteinuria_risk = 2; // 可能蛋白尿

    // KDIGO分级
    if (egfr < 15)
        return 4; // 肾衰竭
    else if (egfr < 30)
        return 3; // 重度下降
    else if (egfr < 45)
    {
        if (proteinuria_risk >= 1)
            return 3; // 重度下降伴蛋白尿
        else
            return 2; // 中度下降
    }
    else if (egfr < 60)
    {
        if (proteinuria_risk >= 1)
            return 2; // 中度下降伴蛋白尿
        else
            return 1; // 轻度下降
    }
    else if (egfr < 90)
    {
        if (proteinuria_risk >= 1)
            return 1; // 轻度下降伴蛋白尿
        else
            return 0; // 正常或轻度下降
    }
    else
        return 0; // 正常
}

// 肿瘤风险评估 (基于常见癌症风险因素)
int assess_oncology_risk(HealthData data)
{
    float bmi = data.weight / (data.height * data.height);
    int score = 0;

    // 1. 基本风险因素
    if (data.age >= 50)
        score += 3;
    else if (data.age >= 40)
        score += 2;

    // 2. 生活方式因素
    if (data.is_smoker)
        score += 4; // 肺癌等风险
    if (bmi >= 28.0)
        score += 2; // 肥胖相关癌症

    // 3. 炎症状态
    if (data.hs_crp >= 3.0)
        score += 2;

    // 4. 代谢因素
    if (data.fasting_glucose >= 7.0)
        score += 1; // 糖尿病与癌症风险
    if (data.ldl >= 4.1)
        score += 1; // 高脂血症与某些癌症

    // 5. 家族史
    if (data.family_history)
        score += 2;

    // 风险分级
    if (score >= 8)
        return 2; // 高风险：建议专业筛查
    else if (score >= 5)
        return 1; // 中等风险：建议基础筛查
    else
        return 0; // 一般风险
}

// 修改主评估函数
RiskResult assess_chronic_disease_risk(HealthData data)
{
    RiskResult result;

    // 调用子模块评估
    result.cvd_risk_level = assess_cvd_risk(data);
    result.diabetes_risk = assess_diabetes_risk(data);
    result.ckd_risk = assess_ckd_risk(data);
    result.oncology_alert = assess_oncology_risk(data);

    // 生成个性化建议（调用封装的建议函数）
    generate_risk_advice(&result, result.advice);

    return result;
}

// 修改打印函数，只打印异常部分
void print_risk_assessment(const RiskResult *risk)
{
    if (risk == NULL)
    {
        printf("[错误] 风险评估数据为空\n");
        return;
    }

    printf("\n======== 慢性病风险评估 ========\n");

    // 只打印异常风险项
    if (risk->cvd_risk_level >= 1)
    {
        printf("心脑血管风险: %s\n",
               (risk->cvd_risk_level == 3) ? "极高危" : (risk->cvd_risk_level == 2) ? "高危"
                                                                                    : "中危");
    }

    if (risk->diabetes_risk >= 1)
    {
        printf("糖尿病风险: %s\n",
               (risk->diabetes_risk == 3) ? "极高危" : (risk->diabetes_risk == 2) ? "高危"
                                                                                  : "中危");
    }

    if (risk->ckd_risk >= 1)
    {
        printf("肾病风险: %s\n",
               (risk->ckd_risk == 4) ? "肾衰竭" : (risk->ckd_risk == 3) ? "重度下降"
                                              : (risk->ckd_risk == 2)   ? "中度下降"
                                                                        : "轻度下降");
    }

    if (risk->oncology_alert >= 1)
    {
        printf("肿瘤预警: %s\n",
               (risk->oncology_alert == 2) ? "高风险" : "中等风险");
    }

    // 打印建议（只有异常时才会打印）
    // if (strcmp(risk->advice, "所有风险评估指标均在正常范围内，请继续保持健康生活方式！\n") != 0)
    // {
    //     printf("\n建议:\n%s", risk->advice);
    // }
    // else
    // {
    //     printf("\n%s", risk->advice);
    // }

    printf("==============================\n");
}