
#ifndef __IMPORT_FILE_DATA_H
#define __IMPORT_FILE_DATA_H

#include "define.h"
// #include "Health_Data.h"

/**
 * @file Import_File_Data.c
 * @brief 健康数据文件导入模块实现
 */

/**
 * @brief 从CSV文件加载健康记录数据
 *
 * 扫描指定目录下的CSV文件，解析并加载健康记录数据。
 * 支持按日期过滤和自动去重功能。
 *
 * @param[in] targetDate 目标日期(格式YYYY-MM-DD)，NULL表示加载所有
 *
 * @note 依赖全局变量 records 和 numRecords
 * @warning 最大记录数受MAX_ENTRIES限制
 */
void loadFromFile(const char *targetDate);

/**
 * @brief 打印已加载的健康记录
 *
 * 以格式化表格显示最新健康记录，包含：
 * - 基本信息
 * - 健康指标分析(BMI、血压分级等)
 * - 生活习惯
 * - 特殊指标
 *
 * @note 自动计算衍生指标(BMI等)
 * @see loadFromFile() 需先加载数据
 */
void printLoadedData();

#endif
