#include "define.h"

// 实现动态检测路径的函数
const char *getSaveDir()
{
    // 优先尝试上一级目录（../File_Save）
    DIR *dir = opendir("../File_Save");
    if (dir != NULL)
    {
        closedir(dir);
        return "../File_Save";
    }

    // 如果上一级没有，尝试当前目录（File_Save）
    dir = opendir("File_Save");
    if (dir != NULL)
    {
        closedir(dir);
        return "File_Save";
    }

    // 如果都找不到，返回 NULL（需在调用时处理错误）
    return NULL;
}