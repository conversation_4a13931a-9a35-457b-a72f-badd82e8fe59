// #include "menu.h"
#include "define.h"

void displayMenu()
{
    // 清屏以提供更整洁的界面
    system("cls");

    // 打印菜单顶部边框
    printf("\n╔════════════════════════════════════╗\n");
    printf("║                                    ║\n");

    // 打印菜单标题
    printf("║       个人健康管理系统 v1.0       ║\n");
    printf("║                                    ║\n");
    printf("╠════════════════════════════════════╣\n");

    // 打印菜单选项
    printf("║  1. 健康信息录入                  ║\n");
    printf("║  2. 健康数据分析                  ║\n");
    printf("║  3. 慢性病评估                    ║\n");
    printf("║  4. 数据查询                      ║\n");
    printf("║  5. 显示已加载数据                ║\n"); // 新增选项，与main.c对应
    printf("║  0. 退出系统                      ║\n");

    printf("║                                    ║\n");
    printf("╚════════════════════════════════════╝\n");
}

// 改进后的getChoice函数，支持更多选项
int getChoice()
{
    char input[100];
    int choice;

    while (1)
    {
        printf("\n请选择操作 [0-5]: "); // 更新提示范围

        if (fgets(input, sizeof(input), stdin) == NULL)
        {
            printf("输入错误，请重新输入。\n");
            clearerr(stdin); // 清除错误状态
            continue;
        }

        // 去除换行符
        input[strcspn(input, "\n")] = '\0';

        // 检查是否为空输入
        if (input[0] == '\0')
        {
            printf("错误：请输入选项数字。\n");
            continue;
        }

        // 检查是否为纯数字
        int valid = 1;
        for (int i = 0; input[i] != '\0'; i++)
        {
            if (!isdigit(input[i]))
            {
                valid = 0;
                break;
            }
        }

        if (!valid)
        {
            printf("错误：请输入0-5的数字。\n"); // 更新错误提示
            continue;
        }

        // 转换为数字并验证范围
        choice = atoi(input);
        if (choice < 0 || choice > 5) // 更新范围检查
        {
            printf("错误：请输入0-5之间的数字。\n");
            continue;
        }

        return choice;
    }
}

// 辅助函数：询问用户是否继续
bool ask_user_continue(const char *prompt)
{
    printf("\n%s (y/n): ", prompt);
    char choice = getchar();
    while (getchar() != '\n')
        ; // 清空输入缓冲区
    return (choice == 'y' || choice == 'Y');
}