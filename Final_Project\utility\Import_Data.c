

#include "define.h"

// // 改进后的inputHealthData函数
// void inputHealthData()
// {
//     extern HealthRecord records[MAX_ENTRIES];
//     extern int numRecords;
//     extern int hasNewData;

//     if (numRecords >= MAX_ENTRIES)
//     {
//         printf("存储已满，无法添加更多记录！\n");
//         return;
//     }

//     HealthRecord newRecord;
//     printf("\n--- 健康信息录入 ---\n");

//     // 自动获取当前日期
//     time_t t = time(NULL);
//     struct tm *tm = localtime(&t);
//     strftime(newRecord.date, sizeof(newRecord.date), "%Y-%m-%d", tm);
//     printf("当前日期: %s\n", newRecord.date);

//     // 基本信息
//     newRecord.age = getIntInput("年龄: ", 0, 120);
//     newRecord.gender = getIntInput("性别 (0:女, 1:男): ", 0, 1);

//     // 身体指标
//     newRecord.height = getFloatInput("身高 (cm): ", 50, 250);
//     newRecord.weight = getFloatInput("体重 (kg): ", 20, 300);

//     // 血压相关
//     newRecord.systolic_bp = getFloatInput("收缩压 (mmHg): ", 50, 250);
//     newRecord.diastolic_bp = getFloatInput("舒张压 (mmHg): ", 30, 150);

//     // 血糖相关
//     newRecord.fasting_glucose = getFloatInput("空腹血糖 (mmol/L): ", 1, 30);
//     newRecord.hba1c = getFloatInput("糖化血红蛋白 A1c (%): ", 2, 20);

//     // 血脂相关
//     newRecord.cholesterol = getFloatInput("总胆固醇 (mmol/L): ", 1, 20);
//     newRecord.ldl = getFloatInput("低密度脂蛋白胆固醇 LDL (mmol/L): ", 0.5, 15);

//     // 其他指标
//     newRecord.creatinine = getFloatInput("肌酐 (umol/L): ", 10, 1000);
//     newRecord.heartRate = getIntInput("心率 (次/分钟): ", 30, 200);
//     newRecord.sleepHours = getFloatInput("睡眠时长 (小时): ", 0, 24);

//     // 生活习惯和家族史
//     newRecord.is_smoker = getIntInput("是否吸烟 (0:否, 1:是): ", 0, 1);
//     newRecord.family_history = getIntInput("家族史 (0:无, 1:有): ", 0, 1);

//     // 特殊指标
//     newRecord.lp_a = getFloatInput("脂蛋白A Lp(a) (mmol/L): ", 0, 1000);
//     newRecord.hs_crp = getFloatInput("高灵敏度C反应蛋白 HS-CRP (mg/L): ", 0, 50);

//     records[numRecords++] = newRecord;
//     hasNewData = 1; // 设置新数据标志
//     printf("\n数据录入成功！\n");
// }

// 改进后的inputHealthData函数
void inputHealthData()
{
    extern HealthRecord records[MAX_ENTRIES];
    extern int numRecords;
    extern int hasNewData;

    if (numRecords >= MAX_ENTRIES)
    {
        printf("存储已满，无法添加更多记录！\n");
        return;
    }

    HealthRecord newRecord;
    memset(&newRecord, 0, sizeof(HealthRecord));

    printf("\n=== 健康信息录入 ===\n");

    // 1. 自动获取当前日期
    time_t t = time(NULL);
    struct tm *tm = localtime(&t);
    strftime(newRecord.date, sizeof(newRecord.date), "%Y-%m-%d", tm);
    printf("当前日期: %s\n", newRecord.date);

    // 2. 获取姓名（必填）
    getNameInput(newRecord.name, sizeof(newRecord.name));

    // 3. 基本信息部分
    printf("\n--- 基本信息 ---\n");
    newRecord.age = getIntInput("年龄: ", 0, 120);
    newRecord.gender = getIntInput("性别 (0:女, 1:男): ", 0, 1);

    // 4. 身体测量部分
    printf("\n--- 身体测量数据 ---\n");
    printf("(直接按Enter可跳过不熟悉的指标)\n");
    newRecord.height = getOptionalFloatInput("身高 (cm) [50-250]: ", 50, 250);
    newRecord.weight = getOptionalFloatInput("体重 (kg) [20-300]: ", 20, 300);
    newRecord.heartRate = getOptionalIntInput("心率 (次/分钟) [30-200]: ", 30, 200);
    newRecord.sleepHours = getOptionalFloatInput("睡眠时长 (小时) [0-24]: ", 0, 24);

    // 5. 血压部分
    printf("\n--- 血压数据 ---\n");
    printf("(正常范围: 收缩压90-120, 舒张压60-80)\n");
    newRecord.systolic_bp = getOptionalFloatInput("收缩压/高压 (mmHg) [50-250]: ", 50, 250);
    newRecord.diastolic_bp = getOptionalFloatInput("舒张压/低压 (mmHg) [30-150]: ", 30, 150);

    // 6. 血糖部分
    printf("\n--- 血糖数据 ---\n");
    printf("(空腹血糖正常值3.9-6.1 mmol/L, 糖化血红蛋白正常值4-6%%)\n");
    newRecord.fasting_glucose = getOptionalFloatInput("空腹血糖 (mmol/L) [1-30]: ", 1, 30);
    newRecord.hba1c = getOptionalFloatInput("糖化血红蛋白A1c (%) [2-20]: ", 2, 20);

    // 7. 血脂部分
    printf("\n--- 血脂数据 ---\n");
    printf("(总胆固醇正常<5.2, LDL正常<3.4 mmol/L)\n");
    newRecord.cholesterol = getOptionalFloatInput("总胆固醇 (mmol/L) [1-20]: ", 1, 20);
    newRecord.ldl = getOptionalFloatInput("低密度脂蛋白/LDL (mmol/L) [0.5-15]: ", 0.5, 15);

    // 8. 其他健康指标
    printf("\n--- 其他健康指标 ---\n");
    newRecord.creatinine = getOptionalFloatInput("肌酐 (umol/L) [肾功能指标, 10-1000]: ", 10, 1000);

    // 9. 生活习惯
    printf("\n--- 生活习惯 ---\n");
    newRecord.is_smoker = getIntInput("是否吸烟 (0:否, 1:是): ", 0, 1);
    newRecord.family_history = getIntInput("是否有心血管疾病家族史 (0:无, 1:有): ", 0, 1);

    // 10. 高级指标 (可选)
    printf("\n--- 高级指标 (可选) ---\n");
    printf("(这些指标通常在医院检查中获得)\n");
    newRecord.lp_a = getOptionalFloatInput("脂蛋白A Lp(a) (mmol/L) [0-1000]: ", 0, 1000);
    newRecord.hs_crp = getOptionalFloatInput("高敏C反应蛋白 HS-CRP (mg/L) [炎症指标, 0-50]: ", 0, 50);

    records[numRecords++] = newRecord;
    hasNewData = 1;
    printf("\n%s的健康数据录入成功！\n", newRecord.name);
}

// 核心输入函数（支持可选和必填）
int getInputCore(const char *prompt, int min, int max, bool optional, bool *skipped)
{
    char input[100];

    while (1)
    {
        printf("%s", prompt);
        if (optional)
        {
            printf("(按Enter跳过) ");
        }

        if (fgets(input, sizeof(input), stdin) == NULL)
        {
            printf("读取输入失败，请重试。\n");
            continue;
        }

        // 检查是否跳过（仅对可选输入有效）
        if (optional && input[0] == '\n')
        {
            if (skipped)
                *skipped = true;
            return NO_INPUT_INT;
        }

        int value;
        if (sscanf(input, "%d", &value) != 1)
        {
            printf("请输入有效的整数。\n");
            continue;
        }

        if (value < min || value > max)
        {
            printf("请输入%d到%d之间的值。\n", min, max);
            continue;
        }

        if (skipped)
            *skipped = false;
        return value;
    }
}

// 浮点数版本
float getFloatInputCore(const char *prompt, float min, float max, bool optional, bool *skipped)
{
    char input[100];

    while (1)
    {
        printf("%s", prompt);
        if (optional)
        {
            printf("(按Enter跳过) ");
        }

        if (fgets(input, sizeof(input), stdin) == NULL)
        {
            printf("读取输入失败，请重试。\n");
            continue;
        }

        // 检查是否跳过
        if (optional && input[0] == '\n')
        {
            if (skipped)
                *skipped = true;
            return NO_INPUT_FLOAT;
        }

        float value;
        if (sscanf(input, "%f", &value) != 1)
        {
            printf("请输入有效的数值。\n");
            continue;
        }

        if (value < min || value > max)
        {
            printf("请输入%.1f到%.1f之间的值。\n", min, max);
            continue;
        }

        if (skipped)
            *skipped = false;
        return value;
    }
}

// 获取姓名的函数
void getNameInput(char *name, int max_length)
{
    char input[100];

    while (1)
    {
        printf("姓名 (最多%d个字符): ", max_length - 1);

        if (fgets(input, sizeof(input), stdin) == NULL)
        {
            printf("读取输入失败，请重试。\n");
            continue;
        }

        // 去除末尾的换行符
        input[strcspn(input, "\n")] = '\0';

        // 检查姓名长度
        int len = strlen(input);
        if (len == 0)
        {
            printf("姓名不能为空，请重新输入。\n");
            continue;
        }

        // 检查是否超过最大长度
        if (len > max_length - 1)
        {
            printf("姓名过长，最多支持%d个字符。\n", max_length - 1);
            continue;
        }

        // 复制到输出缓冲区
        strncpy(name, input, max_length - 1);
        name[max_length - 1] = '\0';
        break;
    }
}

// 简化后的接口函数
int getIntInput(const char *prompt, int min, int max)
{
    return getInputCore(prompt, min, max, false, NULL);
}

int getOptionalIntInput(const char *prompt, int min, int max)
{
    bool skipped;
    return getInputCore(prompt, min, max, true, &skipped);
}

float getFloatInput(const char *prompt, float min, float max)
{
    return getFloatInputCore(prompt, min, max, false, NULL);
}

float getOptionalFloatInput(const char *prompt, float min, float max)
{
    bool skipped;
    return getFloatInputCore(prompt, min, max, true, &skipped);
}

// 辅助函数：安全获取浮点数输入
// 函数用于获取用户输入的浮点数，并限制输入范围
// float getFloatInput(const char *prompt, float min, float max)
// {
//     // 定义变量value用于存储用户输入的浮点数
//     float value;
//     // 定义字符数组input用于存储用户输入的字符串
//     char input[100];

//     // 无限循环，直到用户输入有效的数值
//     while (1)
//     {
//         // 打印提示信息
//         printf("%s", prompt);

//         // 从标准输入读取用户输入的字符串
//         if (fgets(input, sizeof(input), stdin) == NULL)
//         {
//             // 如果读取失败，打印错误信息并继续循环
//             printf("读取输入失败，请重试。\n");
//             continue;
//         }

//         // 将用户输入的字符串转换为浮点数
//         if (sscanf(input, "%f", &value) != 1)
//         {
//             // 如果转换失败，打印错误信息并继续循环
//             printf("请输入有效的数值。\n");
//             continue;
//         }

//         // 如果用户输入的浮点数不在指定范围内，打印错误信息并继续循环
//         if (value < min || value > max)
//         {
//             printf("请输入%.1f到%.1f之间的值。\n", min, max);
//             continue;
//         }

//         // 如果用户输入的浮点数在指定范围内，跳出循环
//         break;
//     }

//     // 返回用户输入的浮点数
//     return value;
// }

// // 辅助函数：安全获取整数输入
// int getIntInput(const char *prompt, int min, int max)
// {
//     // 定义变量value和input
//     int value;
//     char input[100];

//     // 无限循环，直到输入有效
//     while (1)
//     {
//         // 打印提示信息
//         printf("%s", prompt);

//         // 从标准输入读取一行
//         if (fgets(input, sizeof(input), stdin) == NULL)
//         {
//             // 如果读取失败，打印错误信息并继续循环
//             printf("读取输入失败，请重试。\n");
//             continue;
//         }

//         // 将输入转换为整数
//         if (sscanf(input, "%d", &value) != 1)
//         {
//             // 如果转换失败，打印错误信息并继续循环
//             printf("请输入有效的整数。\n");
//             continue;
//         }

//         // 如果输入的值不在指定范围内，打印错误信息并继续循环
//         if (value < min || value > max)
//         {
//             printf("请输入%d到%d之间的值。\n", min, max);
//             continue;
//         }

//         // 如果输入有效，跳出循环
//         break;
//     }

//     // 返回输入的值
//     return value;
// }