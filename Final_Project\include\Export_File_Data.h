
#ifndef __EXPORT_FILE_DATA_H
#define __EXPORT_FILE_DATA_H

#include "define.h"
// #include "Import_Data.h"
/**
 * @brief 将健康记录数组保存到CSV文件
 *
 * 该函数将健康记录数组保存为带时间戳的CSV文件，包含完整的健康数据字段。
 * 自动创建保存目录(SAVE_DIR)，文件名为当前时间格式(YYYYMMDD_HHMMSS.csv)。
 *
 * @param[in] records 健康记录数组
 * @param[in] numRecords 数组中的记录数量
 *
 * @note 线程安全：通过isSavingInProgress标志防止重入
 * @warning 需要确保SAVE_DIR宏已正确定义
 */
void saveToFile(HealthRecord records[], int numRecords);

#endif // __EXPORT_FILE_DATA_H